<template>
  <div class="browser-test-container">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>浏览器兼容性测试</span>
          <el-tag :type="browserInfo.type" size="small">
            {{ browserInfo.name }}
          </el-tag>
        </div>
      </template>
      
      <div class="test-content">
        <div class="browser-info">
          <h4>浏览器信息</h4>
          <p><strong>浏览器:</strong> {{ browserInfo.name }} {{ browserInfo.version }}</p>
          <p><strong>用户代理:</strong> {{ userAgent }}</p>
          <p><strong>屏幕尺寸:</strong> {{ screenSize.width }} x {{ screenSize.height }}</p>
          <p><strong>视口尺寸:</strong> {{ viewportSize.width }} x {{ viewportSize.height }}</p>
        </div>
        
        <div class="css-support-test">
          <h4>CSS特性支持检测</h4>
          <div class="support-grid">
            <div class="support-item">
              <span>CSS Grid:</span>
              <el-tag :type="cssSupport.grid ? 'success' : 'danger'" size="small">
                {{ cssSupport.grid ? '支持' : '不支持' }}
              </el-tag>
            </div>
            <div class="support-item">
              <span>CSS Variables:</span>
              <el-tag :type="cssSupport.variables ? 'success' : 'danger'" size="small">
                {{ cssSupport.variables ? '支持' : '不支持' }}
              </el-tag>
            </div>
            <div class="support-item">
              <span>CSS Inset:</span>
              <el-tag :type="cssSupport.inset ? 'success' : 'danger'" size="small">
                {{ cssSupport.inset ? '支持' : '不支持' }}
              </el-tag>
            </div>
            <div class="support-item">
              <span>Flexbox Gap:</span>
              <el-tag :type="cssSupport.flexGap ? 'success' : 'danger'" size="small">
                {{ cssSupport.flexGap ? '支持' : '不支持' }}
              </el-tag>
            </div>
          </div>
        </div>
        
        <div class="layout-test">
          <h4>布局测试</h4>
          <el-button type="primary" @click="testChatPanelLayout">
            测试聊天面板布局
          </el-button>
          <el-button type="success" @click="testAddUserDialog">
            测试发起对话布局
          </el-button>
          <el-button type="info" @click="openCompatibilityReport">
            生成兼容性报告
          </el-button>
        </div>
        
        <div class="test-results" v-if="testResults.length > 0">
          <h4>测试结果</h4>
          <div class="results-container">
            <div v-for="(result, index) in testResults" :key="index" class="result-item">
              <el-tag :type="result.type" size="small">{{ result.status }}</el-tag>
              <span class="result-text">{{ result.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';

// 响应式数据
const userAgent = ref('');
const screenSize = ref({ width: 0, height: 0 });
const viewportSize = ref({ width: 0, height: 0 });
const cssSupport = ref({
  grid: false,
  variables: false,
  inset: false,
  flexGap: false
});
const testResults = ref([]);

// 浏览器信息
const browserInfo = computed(() => {
  const ua = userAgent.value.toLowerCase();
  
  if (ua.includes('chrome') && !ua.includes('edge')) {
    return { name: 'Chrome', type: 'success', version: getVersion(ua, 'chrome') };
  } else if (ua.includes('firefox')) {
    return { name: 'Firefox', type: 'warning', version: getVersion(ua, 'firefox') };
  } else if (ua.includes('safari') && !ua.includes('chrome')) {
    return { name: 'Safari', type: 'info', version: getVersion(ua, 'safari') };
  } else if (ua.includes('edge')) {
    return { name: 'Edge', type: 'primary', version: getVersion(ua, 'edge') };
  } else {
    return { name: '未知浏览器', type: 'danger', version: '' };
  }
});

// 方法
function getVersion(ua, browser) {
  const regex = new RegExp(`${browser}\/([\\d\\.]+)`);
  const match = ua.match(regex);
  return match ? match[1] : '';
}

function detectCSSSupport() {
  // 检测CSS Grid支持
  cssSupport.value.grid = CSS.supports('display', 'grid');
  
  // 检测CSS Variables支持
  cssSupport.value.variables = CSS.supports('color', 'var(--test)');
  
  // 检测CSS Inset支持
  cssSupport.value.inset = CSS.supports('inset', '0');
  
  // 检测Flexbox Gap支持
  cssSupport.value.flexGap = CSS.supports('gap', '10px') && CSS.supports('display', 'flex');
}

function updateSizes() {
  if (typeof window !== 'undefined') {
    screenSize.value = {
      width: window.screen.width,
      height: window.screen.height
    };
    
    viewportSize.value = {
      width: window.innerWidth,
      height: window.innerHeight
    };
  }
}

function addTestResult(status, message, type = 'info') {
  testResults.value.unshift({
    status,
    message,
    type,
    timestamp: new Date().toLocaleTimeString()
  });
  
  // 限制结果数量
  if (testResults.value.length > 20) {
    testResults.value = testResults.value.slice(0, 20);
  }
}

function testChatPanelLayout() {
  addTestResult('测试', '开始测试聊天面板布局...', 'info');
  
  // 检查关键CSS属性
  const testElement = document.createElement('div');
  testElement.style.display = 'flex';
  testElement.style.flexDirection = 'column';
  document.body.appendChild(testElement);
  
  const computedStyle = window.getComputedStyle(testElement);
  const flexSupport = computedStyle.display === 'flex';
  
  document.body.removeChild(testElement);
  
  if (flexSupport) {
    addTestResult('通过', '聊天面板布局支持正常', 'success');
  } else {
    addTestResult('失败', '聊天面板布局可能有问题', 'danger');
  }
}

function testAddUserDialog() {
  addTestResult('测试', '开始测试发起对话布局...', 'info');
  
  // 模拟发起对话的布局测试
  const issues = [];
  
  if (!cssSupport.value.grid) {
    issues.push('CSS Grid不支持，已使用Flexbox替代');
  }
  
  if (!cssSupport.value.inset) {
    issues.push('CSS Inset不支持，已使用传统定位属性');
  }
  
  if (!cssSupport.value.variables) {
    issues.push('CSS Variables不支持，已添加fallback颜色');
  }
  
  if (issues.length === 0) {
    addTestResult('通过', '发起对话布局完全兼容', 'success');
  } else {
    addTestResult('警告', `发现兼容性问题但已修复: ${issues.join(', ')}`, 'warning');
  }
}

function openCompatibilityReport() {
  const report = {
    browser: browserInfo.value,
    screen: screenSize.value,
    viewport: viewportSize.value,
    cssSupport: cssSupport.value,
    userAgent: userAgent.value
  };
  
  console.log('浏览器兼容性报告:', report);
  
  const reportText = `
浏览器兼容性报告
================
浏览器: ${browserInfo.value.name} ${browserInfo.value.version}
屏幕尺寸: ${screenSize.value.width}x${screenSize.value.height}
视口尺寸: ${viewportSize.value.width}x${viewportSize.value.height}

CSS特性支持:
- CSS Grid: ${cssSupport.value.grid ? '✅' : '❌'}
- CSS Variables: ${cssSupport.value.variables ? '✅' : '❌'}
- CSS Inset: ${cssSupport.value.inset ? '✅' : '❌'}
- Flexbox Gap: ${cssSupport.value.flexGap ? '✅' : '❌'}

建议:
${!cssSupport.value.grid ? '- 使用Flexbox替代CSS Grid\n' : ''}
${!cssSupport.value.inset ? '- 使用top/left/right/bottom替代inset\n' : ''}
${!cssSupport.value.variables ? '- 为CSS变量添加fallback值\n' : ''}
  `;
  
  // 复制到剪贴板
  if (navigator.clipboard) {
    navigator.clipboard.writeText(reportText).then(() => {
      ElMessage.success('兼容性报告已复制到剪贴板');
    });
  } else {
    console.log(reportText);
    ElMessage.info('兼容性报告已输出到控制台');
  }
  
  addTestResult('完成', '兼容性报告已生成', 'success');
}

// 生命周期
onMounted(() => {
  userAgent.value = navigator.userAgent;
  updateSizes();
  detectCSSSupport();
  
  // 监听窗口大小变化
  if (typeof window !== 'undefined') {
    window.addEventListener('resize', updateSizes);
  }
});
</script>

<style scoped>
.browser-test-container {
  padding: 20px;
}

.test-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.browser-info p {
  margin: 8px 0;
  word-break: break-all;
}

.support-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.support-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border: 1px solid #eee;
  border-radius: 4px;
  min-width: 150px;
}

.layout-test {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

.layout-test h4 {
  width: 100%;
  margin-bottom: 10px;
}

.results-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
  font-size: 14px;
}

.result-text {
  flex: 1;
}
</style>
