<template>
  <div class="websocket-test-container">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>WebSocket连接状态测试</span>
          <el-tag :type="connectionStatus.type" size="small">
            {{ connectionStatus.text }}
          </el-tag>
        </div>
      </template>
      
      <div class="test-content">
        <div class="status-info">
          <h4>连接信息</h4>
          <p><strong>全局连接状态:</strong> {{ globalConnectionActive ? '已激活' : '未激活' }}</p>
          <p><strong>WebSocket状态:</strong> {{ webSocketStatus.isConnected ? '已连接' : '未连接' }}</p>
          <p><strong>重连次数:</strong> {{ webSocketStatus.reconnectAttempts }}</p>
          <p><strong>连接状态码:</strong> {{ getReadyStateText(webSocketStatus.readyState) }}</p>
        </div>
        
        <div class="test-actions">
          <h4>测试操作</h4>
          <el-button type="primary" @click="startConnection" :disabled="globalConnectionActive">
            启动全局连接
          </el-button>
          <el-button type="success" @click="refreshStatus">
            刷新状态
          </el-button>
          <el-button type="info" @click="sendTestMessage" :disabled="!webSocketStatus.isConnected">
            发送测试消息
          </el-button>
          <el-button type="danger" @click="sendWrongContextMessage" :disabled="!webSocketStatus.isConnected">
            发送错误上下文消息
          </el-button>
          <el-button type="warning" @click="testUnreadCount">
            测试未读消息更新
          </el-button>
          <el-button type="warning" @click="simulatePageSwitch">
            模拟页面切换
          </el-button>
        </div>
        
        <div class="message-log">
          <h4>消息日志</h4>
          <div class="log-container">
            <div v-for="(log, index) in messageLogs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span class="log-content">{{ log.message }}</span>
            </div>
          </div>
          <el-button size="small" @click="clearLogs">清空日志</el-button>
        </div>
      </div>
    </el-card>
    
    <el-card class="instruction-card" style="margin-top: 20px;">
      <template #header>
        <span>测试说明</span>
      </template>
      <div class="instructions">
        <h4>测试步骤：</h4>
        <ol>
          <li><strong>连接持久化测试：</strong>
            <ul>
              <li>点击"启动全局连接"建立WebSocket连接</li>
              <li>观察连接状态变化</li>
              <li>点击"模拟页面切换"，观察连接是否保持</li>
              <li>切换到其他页面（如用户管理、系统设置等），再回到此页面</li>
              <li>检查连接状态是否依然保持</li>
            </ul>
          </li>
          <li><strong>消息上下文过滤测试：</strong>
            <ul>
              <li>点击"发送错误上下文消息"按钮</li>
              <li>观察日志输出，确认错误上下文的消息被正确过滤</li>
              <li>前往聊天页面，选择一个用户进行聊天</li>
              <li>在聊天页面发送消息，确认消息只显示在正确的聊天窗口中</li>
            </ul>
          </li>
          <li><strong>未读消息更新测试：</strong>
            <ul>
              <li>点击"测试未读消息更新"按钮</li>
              <li>观察智能助手按钮上的红色徽章是否更新</li>
              <li>打开智能助手，检查左侧用户列表是否显示未读数量</li>
              <li>选择有未读消息的用户，确认未读数量清零</li>
            </ul>
          </li>
        </ol>
        <h4>预期结果：</h4>
        <ul>
          <li><strong>连接持久化：</strong>连接应该在页面切换后依然保持活跃状态，不会因为组件卸载而断开</li>
          <li><strong>消息过滤：</strong>只有属于当前聊天上下文的消息才会显示在聊天窗口中</li>
          <li><strong>错误消息拦截：</strong>不属于当前会话的消息应该被正确过滤，不会错误显示</li>
          <li><strong>未读消息实时更新：</strong>收到新消息时，左侧用户列表应该实时更新未读数量和最后消息</li>
          <li><strong>未读数量清零：</strong>选择用户进行聊天时，该用户的未读数量应该自动清零</li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import {
  startGlobalChatWebSocket,
  isGlobalChatWebSocketActive,
  getChatWebSocket
} from '@/utils/websocket';
import { useChatStore } from '@/store/modules/chat';
import useUserStore from '@/store/modules/user';

// 响应式数据
const globalConnectionActive = ref(false);
const webSocketStatus = ref({
  isConnected: false,
  reconnectAttempts: 0,
  readyState: WebSocket.CLOSED
});
const messageLogs = ref([]);

// Store实例
const chatStore = useChatStore();
const userStore = useUserStore();

// 计算属性
const connectionStatus = computed(() => {
  if (globalConnectionActive.value && webSocketStatus.value.isConnected) {
    return { type: 'success', text: '连接正常' };
  } else if (globalConnectionActive.value && !webSocketStatus.value.isConnected) {
    return { type: 'warning', text: '连接中断' };
  } else {
    return { type: 'danger', text: '未连接' };
  }
});

// 方法
function addLog(message) {
  const now = new Date();
  const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
  messageLogs.value.unshift({
    time: timeStr,
    message: message
  });
  
  // 限制日志数量
  if (messageLogs.value.length > 50) {
    messageLogs.value = messageLogs.value.slice(0, 50);
  }
}

function startConnection() {
  try {
    addLog('尝试启动全局WebSocket连接...');
    const webSocket = startGlobalChatWebSocket();
    
    // 设置事件监听器
    webSocket.on('connected', () => {
      addLog('WebSocket连接成功');
      refreshStatus();
    });

    webSocket.on('disconnected', () => {
      addLog('WebSocket连接断开');
      refreshStatus();
    });

    webSocket.on('message', (data) => {
      addLog(`收到消息: ${JSON.stringify(data)}`);
    });

    webSocket.on('error', (error) => {
      addLog(`WebSocket错误: ${error}`);
      refreshStatus();
    });
    
    refreshStatus();
    ElMessage.success('WebSocket连接启动成功');
  } catch (error) {
    addLog(`启动连接失败: ${error.message}`);
    ElMessage.error('WebSocket连接启动失败');
  }
}

function refreshStatus() {
  globalConnectionActive.value = isGlobalChatWebSocketActive();
  
  try {
    const webSocket = getChatWebSocket();
    if (webSocket) {
      const status = webSocket.getStatus();
      webSocketStatus.value = status;
    }
  } catch (error) {
    console.error('获取WebSocket状态失败:', error);
  }
  
  addLog(`状态刷新 - 全局连接: ${globalConnectionActive.value}, WebSocket连接: ${webSocketStatus.value.isConnected}`);
}

function sendTestMessage() {
  try {
    const webSocket = getChatWebSocket();
    const success = webSocket.send({
      type: 'test',
      message: 'WebSocket连接测试消息',
      timestamp: Date.now()
    });

    if (success) {
      addLog('测试消息发送成功');
      ElMessage.success('测试消息发送成功');
    } else {
      addLog('测试消息发送失败');
      ElMessage.error('测试消息发送失败');
    }
  } catch (error) {
    addLog(`发送测试消息失败: ${error.message}`);
    ElMessage.error('发送测试消息失败');
  }
}

// 发送错误上下文的消息，测试消息过滤逻辑
function sendWrongContextMessage() {
  try {
    const currentUserId = userStore.id;

    // 模拟一个不属于当前聊天上下文的消息
    const wrongContextMessage = {
      oid: 'test_wrong_' + Date.now(),
      content: '这是一条错误上下文的测试消息',
      senderId: 'fake_user_123', // 假的发送者ID
      receiverId: 'fake_user_456', // 假的接收者ID
      receiveTarget: 'fake_user_456',
      senderName: '假用户A',
      createTime: Date.now(),
      isImage: 0,
      chatReadStatus: 0
    };

    addLog(`尝试添加错误上下文消息: 发送者=${wrongContextMessage.senderId}, 接收者=${wrongContextMessage.receiverId}`);
    addLog(`当前用户ID: ${currentUserId}, 当前聊天对象: ${chatStore.currentUserOid}`);

    // 直接调用store的消息添加方法
    const result = chatStore.addOneChatMsgDataByWS(wrongContextMessage);

    if (result) {
      addLog('❌ 错误：不应该添加的消息被添加了！');
      ElMessage.error('消息过滤逻辑有问题：错误上下文的消息被添加了');
    } else {
      addLog('✅ 正确：错误上下文的消息被正确过滤');
      ElMessage.success('消息过滤逻辑正常：错误上下文的消息被正确过滤');
    }
  } catch (error) {
    addLog(`发送错误上下文消息测试失败: ${error.message}`);
    ElMessage.error('测试失败');
  }
}

// 测试未读消息更新
function testUnreadCount() {
  try {
    const currentUserId = userStore.id;

    // 模拟一个来自其他用户的消息
    const testMessage = {
      oid: 'test_unread_' + Date.now(),
      content: '这是一条测试未读消息更新的消息',
      senderId: 'test_user_123', // 假的发送者ID
      receiverId: currentUserId, // 发给当前用户
      receiveTarget: currentUserId,
      senderName: '测试用户A',
      createTime: Date.now(),
      isImage: 0,
      chatReadStatus: 0
    };

    addLog(`模拟收到新消息: 发送者=${testMessage.senderId}, 内容=${testMessage.content}`);

    // 触发自定义事件，模拟WebSocket消息
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      const event = new CustomEvent('chatMessageReceived', {
        detail: {
          senderId: testMessage.senderId,
          receiverId: testMessage.receiverId,
          content: testMessage.content,
          createTime: testMessage.createTime
        }
      });
      window.dispatchEvent(event);

      addLog('✅ 已触发chatMessageReceived事件，检查左侧用户列表是否更新');
      ElMessage.success('测试事件已触发，请检查聊天面板的用户列表更新');
    } else {
      addLog('❌ 无法触发自定义事件');
      ElMessage.error('无法触发测试事件');
    }
  } catch (error) {
    addLog(`测试未读消息更新失败: ${error.message}`);
    ElMessage.error('测试失败');
  }
}

function simulatePageSwitch() {
  addLog('模拟页面切换 - 记录当前连接状态');
  const currentStatus = {
    global: globalConnectionActive.value,
    connected: webSocketStatus.value.isConnected
  };
  
  setTimeout(() => {
    refreshStatus();
    addLog(`页面切换后状态 - 全局连接: ${globalConnectionActive.value}, WebSocket连接: ${webSocketStatus.value.isConnected}`);
    
    if (currentStatus.global === globalConnectionActive.value && 
        currentStatus.connected === webSocketStatus.value.isConnected) {
      addLog('✅ 连接状态保持一致，测试通过');
      ElMessage.success('连接状态保持正常');
    } else {
      addLog('❌ 连接状态发生变化，可能存在问题');
      ElMessage.warning('连接状态发生变化');
    }
  }, 1000);
}

function clearLogs() {
  messageLogs.value = [];
  addLog('日志已清空');
}

function getReadyStateText(readyState) {
  switch (readyState) {
    case WebSocket.CONNECTING: return 'CONNECTING (0)';
    case WebSocket.OPEN: return 'OPEN (1)';
    case WebSocket.CLOSING: return 'CLOSING (2)';
    case WebSocket.CLOSED: return 'CLOSED (3)';
    default: return `UNKNOWN (${readyState})`;
  }
}

// 生命周期
onMounted(() => {
  addLog('WebSocket测试页面已加载');
  refreshStatus();
  
  // 定期刷新状态
  const interval = setInterval(refreshStatus, 5000);
  
  onUnmounted(() => {
    clearInterval(interval);
    addLog('WebSocket测试页面已卸载，但连接应该保持');
  });
});
</script>

<style scoped>
.websocket-test-container {
  padding: 20px;
}

.test-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.status-info p {
  margin: 8px 0;
}

.test-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

.test-actions h4 {
  width: 100%;
  margin-bottom: 10px;
}

.message-log {
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  margin: 10px 0;
  background-color: #f9f9f9;
}

.log-item {
  display: flex;
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #666;
  margin-right: 10px;
  min-width: 60px;
}

.log-content {
  color: #333;
}

.instruction-card {
  max-width: 800px;
  margin: 0 auto;
}

.instructions ol {
  padding-left: 20px;
}

.instructions li {
  margin-bottom: 8px;
}
</style>
