import request from '@/utils/request';

// 获取聊天用户列表
export function getChatUserList(params) {
    return request({
        url: '/system/message/chat/list',
        method: 'post',
        data: { keyWord: params.keyword }
    });
}

// 获取聊天消息分页
export function getChatMessagePage(params) {
    return request({
        url: '/system/message/chat/page',
        method: 'post',
        data: {
            current: params.pageNum || 1,
            pageSize: params.pageSize || 10,
            userOid: params.userOid,
            keyword: params.keyword
        }
    });
}

// 标记所有消息已读
export function markAllChatMessagesAsRead(userOid) {
    return request({
        url: '/system/message/read/all',
        method: 'post',
        data: { userOid }
    });
}

// 标记指定消息已读
export function markChatMessagesAsRead(messageIds) {
    return request({
        url: '/system/message/read',
        method: 'post',
        data: { messageIds }
    });
}

// 删除聊天消息
export function deleteChatMessage(userOid) {
    return request({
        url: '/system/message/del/chat',
        method: 'post',
        data: { userOid }
    });
}

// 发送聊天消息
export function sendChatMessage(data) {
    return request({
        url: '/system/message/chat/send',
        method: 'post',
        data: {
            content: data.content,
            receiveTarget: data.receiverId || data.receiveTarget,
            isImage: data.isImage || 0,
            quoteOid: data.quoteOid,
            quoteContent: data.quoteContent
        }
    });
}

// 获取未读消息数量
export function getUnreadMessageCount() {
    return request({
        url: '/system/message/unread/count',
        method: 'get'
    });
}

// 聊天图片上传
export function uploadChatImage(file) {
    const formData = new FormData();
    formData.append('file', file);

    return request({
        url: '/system/message/upload/image',
        method: 'post',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });
}

// 聊天文件上传
export function uploadChatFile(file) {
    const formData = new FormData();
    formData.append('file', file);

    return request({
        url: '/system/message/upload/file',
        method: 'post',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });
}

