<template>
  <div class="chat-page">
    <el-row :gutter="16">
      <el-col :span="6">
        <el-card class="chat-left" shadow="never">
          <div class="flex-between">
            <span>会话列表</span>
            <el-input v-model="keyword" placeholder="搜索" size="small" clearable @clear="loadUsers" @keyup.enter="loadUsers" style="width: 60%" />
          </div>
          <el-scrollbar height="70vh" class="mt-12">
            <el-menu :default-active="currentUserOid || ''" @select="onSelectUser">
              <el-menu-item v-for="u in userList" :key="u.userId" :index="u.userId">
                <el-avatar :size="24" :icon="UserFilled" class="mr-8" />
                <span>{{ u.nickName }}</span>
              </el-menu-item>
            </el-menu>
          </el-scrollbar>
        </el-card>
      </el-col>

      <el-col :span="18">
        <el-card class="chat-right" shadow="never">
          <template #header>
            <div class="flex-between">
              <div>
                <strong>聊天</strong>
                <span v-if="currentUserName" class="ml-8 text-sub">- {{ currentUserName }}</span>
              </div>
              <div>
                <el-button size="small" type="primary" :disabled="!currentUserOid" @click="markAllRead">标记已读</el-button>
                <el-popconfirm title="确认清空与该用户的聊天记录？" @confirm="clearDialog">
                  <template #reference>
                    <el-button size="small" type="danger" :disabled="!currentUserOid">清空对话</el-button>
                  </template>
                </el-popconfirm>
              </div>
            </div>
          </template>

          <div v-if="!currentUserOid" class="chat-empty">请选择左侧会话开始聊天</div>
          <template v-else>
            <el-scrollbar ref="scrollRef" height="60vh" @scroll="onScroll">
              <div class="msg-list">
                <div v-for="(msg, idx) in chatStore.chatMessages" :key="idx" class="msg-item" :class="{ me: msg.senderId === userInfo.userId }">
                  <el-avatar :size="28" :icon="UserFilled" />
                  <div class="msg-bubble">
                    <!-- 引用消息显示 -->
                    <div v-if="msg.quoteContent" class="quote-message">
                      <div class="quote-content">{{ msg.quoteContent }}</div>
                    </div>

                    <!-- 消息内容 -->
                    <div class="content">
                      <!-- 图片消息 -->
                      <div v-if="msg.isImage === 1" class="image-message">
                        <el-image
                          :src="msg.content"
                          :preview-src-list="[msg.content]"
                          fit="cover"
                          style="max-width: 200px; max-height: 200px;"
                          @error="handleImageError"
                        />
                      </div>
                      <!-- 文本消息 -->
                      <div v-else>{{ msg.content }}</div>
                    </div>

                    <div class="meta">
                      <span>{{ formatTime(msg.createTime) }}</span>
                      <span class="ml-8" :class="{ read: msg.chatReadStatus === 1 }">{{ msg.chatReadStatus === 1 ? '已读' : '未读' }}</span>
                      <!-- 消息操作按钮 -->
                      <el-dropdown class="ml-8" trigger="click" @command="handleMessageAction">
                        <el-button size="small" text>
                          <el-icon><MoreFilled /></el-icon>
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item :command="{action: 'quote', message: msg}">引用</el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </div>
                </div>
              </div>
            </el-scrollbar>

            <div class="chat-input">
              <!-- 引用消息显示 -->
              <div v-if="quoteMessage" class="quote-preview">
                <div class="quote-header">
                  <span>引用消息</span>
                  <el-button size="small" text @click="cancelQuote">
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
                <div class="quote-content">{{ quoteMessage.content }}</div>
              </div>

              <!-- WebSocket连接状态 -->
              <div v-if="!chatStore.webSocketConnected" class="connection-status">
                <el-alert title="WebSocket连接已断开，消息可能无法实时推送" type="warning" :closable="false" />
              </div>

              <el-input
                v-model="text"
                :rows="2"
                type="textarea"
                placeholder="输入消息..."
                @keyup.enter.exact="send"
              />

              <div class="mt-8 flex-between">
                <div class="input-actions">
                  <!-- 图片上传 -->
                  <el-upload
                    ref="imageUploadRef"
                    :action="uploadAction"
                    :headers="uploadHeaders"
                    :show-file-list="false"
                    :before-upload="beforeImageUpload"
                    :on-success="handleImageUploadSuccess"
                    :on-error="handleImageUploadError"
                    accept="image/*"
                  >
                    <el-button size="small" :icon="Picture">图片</el-button>
                  </el-upload>

                  <!-- 文件上传 -->
                  <el-upload
                    ref="fileUploadRef"
                    :action="fileUploadAction"
                    :headers="uploadHeaders"
                    :show-file-list="false"
                    :before-upload="beforeFileUpload"
                    :on-success="handleFileUploadSuccess"
                    :on-error="handleFileUploadError"
                    class="ml-8"
                  >
                    <el-button size="small" :icon="Document">文件</el-button>
                  </el-upload>
                </div>

                <el-button type="primary" :disabled="!canSend" @click="send">发送</el-button>
              </div>
            </div>
          </template>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import { UserFilled, MoreFilled, Close, Picture, Document } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useChatStore } from '@/store/modules/chat';
import { getChatUserList, sendChatMessage, uploadChatImage, uploadChatFile } from '@/api/qh/chat';
import { getToken } from '@/utils/auth';
import { startGlobalChatWebSocket, isGlobalChatWebSocketActive } from '@/utils/websocket';
import useUserStore from '@/store/modules/user';

const chatStore = useChatStore();
const userStore = useUserStore();
// 统一兼容本项目的用户存储结构，提供 userId/nickName 字段给本页使用
const userInfo = computed(() => ({
  userId: userStore.id,
  nickName: userStore.name,
}));

const userList = ref([]);
const keyword = ref('');
const currentUserOid = computed(() => chatStore.currentUserOid);
const currentUser = computed(() => userList.value.find((u) => u.userId === currentUserOid.value));
const currentUserName = computed(() => (currentUser.value && currentUser.value.nickName) || '');

const scrollRef = ref();
const text = ref('');
const quoteMessage = ref(null); // 引用的消息
const imageUploadRef = ref();
const fileUploadRef = ref();

// 上传相关配置
const uploadAction = '/system/chat/upload/image';
const fileUploadAction = '/system/chat/upload/file';
const uploadHeaders = computed(() => ({
  'Authorization': 'Bearer ' + getToken()
}));

// 计算是否可以发送消息
const canSend = computed(() => {
  return (text.value.trim() || quoteMessage.value) && currentUserOid.value;
});

function formatTime(t) {
  if (!t) return '';
  const d = new Date(Number(t));
  const p = (n) => (n < 10 ? `0${n}` : `${n}`);
  return `${d.getFullYear()}-${p(d.getMonth() + 1)}-${p(d.getDate())} ${p(d.getHours())}:${p(d.getMinutes())}`;
}

// 初始化WebSocket连接
function initWebSocket() {
  try {
    if (!isGlobalChatWebSocketActive()) {
      console.log('启动全局WebSocket连接');
      startGlobalChatWebSocket();
    } else {
      console.log('全局WebSocket连接已激活');
    }
  } catch (error) {
    console.error('WebSocket初始化失败:', error);
    ElMessage.error('WebSocket连接失败，消息可能无法实时推送');
  }
}

async function loadUsers() {
  try {
    const resp = await getChatUserList({ keyword: keyword.value });
    // 适配多种返回结构：data.rows / rows / data.list / list / data.records / records
    const rows = resp?.data?.rows || resp?.rows || resp?.data?.list || resp?.list || resp?.data?.records || resp?.records || [];
    // 统一字段命名，供本页使用
    userList.value = rows.map((u) => ({
      userId: u.userId || u.oid || u.id,
      nickName: u.nickName || u.userName || u.name,
      phone: u.phonenumber || u.phone,
    })).filter(u => u.userId);
  } catch (error) {
    console.error('加载用户列表失败:', error);
    ElMessage.error('加载用户列表失败');
  }
}

async function onSelectUser(oid) {
  chatStore.setCurrentUser(oid);
  await chatStore.fetchChatMessage(oid, true);
  await nextTick();
  scrollToBottom();
}

function scrollToBottom() {
  const wrap = scrollRef.value && scrollRef.value.wrapRef;
  if (wrap) wrap.scrollTop = wrap.scrollHeight;
}

async function onScroll({ scrollTop }) {
  if (scrollTop <= 0 && currentUserOid.value && chatStore.page.hasMore) {
    await chatStore.fetchChatMessage(currentUserOid.value, false);
  }
}

async function markAllRead() {
  if (!currentUserOid.value) return;
  await chatStore.markAllChatAsRead(currentUserOid.value);
}

async function clearDialog() {
  if (!currentUserOid.value) return;
  await chatStore.deleteChatMessage(currentUserOid.value);
}

// 处理消息操作
function handleMessageAction(command) {
  const { action, message } = command;

  switch (action) {
    case 'quote':
      quoteMessage.value = message;
      break;
    default:
      console.log('未知操作:', action);
  }
}

// 取消引用
function cancelQuote() {
  quoteMessage.value = null;
}

// 处理图片错误
function handleImageError() {
  ElMessage.error('图片加载失败');
}

// 图片上传前验证
function beforeImageUpload(file) {
  const isImage = file.type.startsWith('image/');
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!');
    return false;
  }
  return true;
}

// 图片上传成功
async function handleImageUploadSuccess(response) {
  if (response.code === 200) {
    const imageUrl = response.data.url;
    await sendImageMessage(imageUrl);
    ElMessage.success('图片发送成功');
  } else {
    ElMessage.error(response.msg || '图片上传失败');
  }
}

// 图片上传失败
function handleImageUploadError(error) {
  console.error('图片上传失败:', error);
  ElMessage.error('图片上传失败');
}

// 文件上传前验证
function beforeFileUpload(file) {
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!');
    return false;
  }
  return true;
}

// 文件上传成功
async function handleFileUploadSuccess(response) {
  if (response.code === 200) {
    const fileUrl = response.data.url;
    const fileName = response.data.originalName;
    await sendFileMessage(fileUrl, fileName);
    ElMessage.success('文件发送成功');
  } else {
    ElMessage.error(response.msg || '文件上传失败');
  }
}

// 文件上传失败
function handleFileUploadError(error) {
  console.error('文件上传失败:', error);
  ElMessage.error('文件上传失败');
}

// 发送图片消息
async function sendImageMessage(imageUrl) {
  const msg = {
    content: imageUrl,
    receiveTarget: currentUserOid.value,
    isImage: 1,
    quoteOid: quoteMessage.value?.messageId,
    quoteContent: quoteMessage.value?.content
  };

  try {
    // 先本地追加，立即显示（优化用户体验）
    const localMsg = {
      ...msg,
      messageId: 'temp_' + Date.now(),
      senderId: userInfo.value.userId,
      senderName: userInfo.value.nickName,
      createTime: Date.now(),
      chatReadStatus: 0,
    };
    chatStore.addOneChatMsgDataByWS(localMsg);

    // 立即滚动到底部显示新消息
    await nextTick();
    scrollToBottom();

    // 再提交到后端
    await sendChatMessage(msg);

    // 成功后刷新当前会话，确保与后端数据一致
    if (currentUserOid.value) {
      await chatStore.fetchChatMessage(currentUserOid.value, true);
      // 等待DOM更新后再次滚动到底部
      await nextTick();
      scrollToBottom();
    }

    // 清空引用
    quoteMessage.value = null;
  } catch (error) {
    console.error('发送图片消息失败:', error);
    ElMessage.error('发送图片消息失败');
  }
}

// 发送文件消息
async function sendFileMessage(fileUrl, fileName) {
  const msg = {
    content: `[文件] ${fileName}: ${fileUrl}`,
    receiveTarget: currentUserOid.value,
    isImage: 0,
    quoteOid: quoteMessage.value?.messageId,
    quoteContent: quoteMessage.value?.content
  };

  try {
    // 先本地追加，立即显示（优化用户体验）
    const localMsg = {
      ...msg,
      messageId: 'temp_' + Date.now(),
      senderId: userInfo.value.userId,
      senderName: userInfo.value.nickName,
      createTime: Date.now(),
      chatReadStatus: 0,
    };
    chatStore.addOneChatMsgDataByWS(localMsg);

    // 立即滚动到底部显示新消息
    await nextTick();
    scrollToBottom();

    // 再提交到后端
    await sendChatMessage(msg);

    // 成功后刷新当前会话，确保与后端数据一致
    if (currentUserOid.value) {
      await chatStore.fetchChatMessage(currentUserOid.value, true);
      // 等待DOM更新后再次滚动到底部
      await nextTick();
      scrollToBottom();
    }

    // 清空引用
    quoteMessage.value = null;
  } catch (error) {
    console.error('发送文件消息失败:', error);
    ElMessage.error('发送文件消息失败');
  }
}

async function send() {
  if (!canSend.value) return;

  const msg = {
    content: text.value.trim(),
    receiveTarget: currentUserOid.value,
    isImage: 0,
    quoteOid: quoteMessage.value?.messageId,
    quoteContent: quoteMessage.value?.content
  };

  try {
    // 先本地追加，立即显示（优化用户体验）
    const localMsg = {
      ...msg,
      messageId: 'temp_' + Date.now(),
      senderId: userInfo.value.userId,
      senderName: userInfo.value.nickName,
      createTime: Date.now(),
      chatReadStatus: 0
    };
    chatStore.addOneChatMsgDataByWS(localMsg);

    // 立即滚动到底部显示新消息
    await nextTick();
    scrollToBottom();

    // 再提交到后端
    await sendChatMessage(msg);

    // 成功后刷新当前会话，确保与后端数据一致
    if (currentUserOid.value) {
      await chatStore.fetchChatMessage(currentUserOid.value, true);
      // 等待DOM更新后再次滚动到底部
      await nextTick();
      scrollToBottom();
    }

    // 清空输入和引用
    text.value = '';
    quoteMessage.value = null;

  } catch (error) {
    console.error('发送消息失败:', error);
    ElMessage.error('发送消息失败');
  }
}

onMounted(async () => {
  await loadUsers();
  initWebSocket();
  // 初始化时更新未读消息数量
  await chatStore.updateUnreadCount();
});

onUnmounted(() => {
  // 页面卸载时不关闭WebSocket连接，保持全局连接持久化
  console.log('聊天页面卸载，保持WebSocket连接持久化');
  // 不调用 closeChatWebSocket()，让连接保持活跃
});
</script>

<style scoped>
.chat-page { padding: 12px; }
.chat-left .el-menu { border-right: none; }
.chat-right { min-height: 74vh; }
.chat-empty { height: 60vh; display: flex; align-items: center; justify-content: center; color: var(--el-text-color-secondary); }
.msg-list { padding: 8px 4px; }
.msg-item { display: flex; align-items: flex-end; margin: 8px 0; gap: 8px; }
.msg-item.me { flex-direction: row-reverse; }
.msg-bubble { max-width: 70%; background: var(--el-fill-color-light); padding: 8px 12px; border-radius: 8px; position: relative; }
.msg-item.me .msg-bubble { background: var(--el-color-primary-light-9); }
.content { white-space: pre-wrap; word-break: break-word; }
.meta { margin-top: 4px; font-size: 12px; color: var(--el-text-color-secondary); display: flex; align-items: center; }
.meta .read { color: var(--el-color-success); }

/* 引用消息样式 */
.quote-message {
  background: var(--el-fill-color-darker);
  border-left: 3px solid var(--el-color-primary);
  padding: 6px 8px;
  margin-bottom: 6px;
  border-radius: 4px;
}
.quote-content {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  max-height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 引用预览样式 */
.quote-preview {
  background: var(--el-fill-color-light);
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;
}
.quote-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

/* 图片消息样式 */
.image-message {
  border-radius: 4px;
  overflow: hidden;
}

/* 连接状态样式 */
.connection-status {
  margin-bottom: 8px;
}

/* 输入操作按钮样式 */
.input-actions {
  display: flex;
  align-items: center;
}

/* 工具类 */
.flex-between { display: flex; justify-content: space-between; align-items: center; }
.flex-end { display: flex; justify-content: flex-end; }
.ml-8 { margin-left: 8px; }
.mr-8 { margin-right: 8px; }
.mt-8 { margin-top: 8px; }
.mt-12 { margin-top: 12px; }
.text-sub { color: var(--el-text-color-secondary); }
</style>

