<template>
  <div class="help-container">
    <section class="help-section">
      <h3 class="help-title">
        <el-icon class="title-icon"><HelpFilled /></el-icon>
        <span>基本功能指南</span>
      </h3>
      <el-collapse>
        <el-collapse-item v-for="(sec, idx) in guideSections" :key="idx" :name="`g-${idx}`">
          <template #title>
            <span class="sec-index">{{ idx + 1 }}.</span>
            <span>{{ sec.title }}</span>
          </template>
          <div class="panel-body">
            <ul class="guide-list">
              <li v-for="(it, i) in sec.items" :key="i">{{ it }}</li>
            </ul>
          </div>
        </el-collapse-item>
      </el-collapse>
    </section>

    <section v-if="faqs && faqs.length" class="help-section">
      <h3 class="help-title">
        <el-icon class="title-icon"><QuestionFilled /></el-icon>
        <span>常见问题</span>
      </h3>
      <el-collapse>
        <el-collapse-item v-for="(f, idx) in faqs" :key="idx" :name="`faq-${idx}`">
          <template #title>
            <span class="sec-index">{{ idx + 1 }}.</span>
            <span>{{ f.title }}</span>
          </template>
          <div class="panel-body">
            <p class="faq-text">{{ f.content }}</p>
          </div>
        </el-collapse-item>
      </el-collapse>
    </section>

    <section v-if="downloadFiles && downloadFiles.length" class="help-section">
      <h3 class="help-title">
        <el-icon class="title-icon"><Document /></el-icon>
        <span>资料下载</span>
      </h3>
      <div class="download-grid">
        <div v-for="file in downloadFiles" :key="file.id" class="download-item">
          <div class="file-meta">
            <div class="file-name">
              <el-tag size="small" :type="getFileBadge(file).type" effect="plain" class="file-badge">{{ getFileBadge(file).label }}</el-tag>
              {{ file.name }}
            </div>
            <div class="file-desc">{{ file.description }}</div>
          </div>
          <el-button type="primary" link @click="handleDownload(file)">
            <el-icon v-if="getFileBadge(file).label === '视频'">
              <VideoPlay />
            </el-icon>
            {{ getFileBadge(file).label === '视频' ? '播放' : '下载' }}
          </el-button>
        </div>
      </div>
      <el-dialog v-model="videoVisible" title="系统使用视频教程" width="800px" :append-to-body="true" :close-on-click-modal="false">
        <video v-if="videoVisible" :src="currentVideo" controls style="width: 100%">您的浏览器不支持 video 标签。</video>
      </el-dialog>
    </section>
  </div>
  
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { HelpFilled, QuestionFilled, Document, VideoPlay } from '@element-plus/icons-vue';
import { guideSections, faqs, downloadFiles } from './helperData';

const videoVisible = ref(false);
const currentVideo = ref('');

function handleDownload(file) {
  if (file.type === 'video') {
    currentVideo.value = file.url;
    videoVisible.value = true;
  } else {
    // 可替换为真实下载逻辑
    // import { download } from '@/utils/request';
    // download(file.url, {}, file.name)
    if (file.url) {
      window.open(file.url, '_blank');
    } else {
      ElMessage.warning(`功能完善中：${file.name}`);
    }
  }
}

function getFileBadge(file) {
  const name = (file && file.name) || '';
  const type = (file && file.type) || '';
  const ext = name.split('.').pop()?.toLowerCase();
  // 优先按类型字段判断，其次按扩展名
  if (type === 'video' || ext === 'mp4' || ext === 'mov' || ext === 'avi') {
    return { label: '视频', type: 'warning' };
  }
  if (ext === 'pdf') {
    return { label: 'PDF', type: 'danger' };
  }
  if (ext === 'xlsx' || ext === 'xls' || type === 'excel') {
    return { label: 'Excel', type: 'success' };
  }
  if (ext === 'doc' || ext === 'docx' || type === 'word') {
    return { label: 'Word', type: 'info' };
  }
  return { label: '文件', type: 'info' };
}
</script>

<style scoped>
.help-container { padding: 16px; max-width: 840px; margin: 0 auto; max-height: 80vh; overflow-y: auto; scrollbar-gutter: stable both-edges; }
.help-section { margin-bottom: 20px; }
.help-title { display: flex; align-items: center; gap: 8px; font-size: 16px; font-weight: 500; margin: 0 0 12px; }
.title-tag { line-height: 1; }
.title-icon { color: var(--el-color-primary); }
.guide-list { margin: 0; padding-left: 12px; list-style: none; }
.guide-list li { position: relative; line-height: 1.8; color: var(--el-text-color-regular); padding-left: 12px; }
.guide-list li::before { content: ""; position: absolute; left: 0; top: 0.9em; transform: translateY(-50%); width: 4px; height: 4px; border-radius: 50%; background: var(--el-color-primary-light-5); }
.sec-index { color: var(--el-text-color-primary); margin-right: 6px; font-weight: 500; }
.panel-body { padding: 10px 14px; background: var(--el-bg-color); border: 1px solid var(--el-border-color); border-left: 2px solid var(--el-border-color); border-radius: 6px; margin: 8px 10px 10px; }
.faq-text { margin: 0; color: var(--el-text-color-regular); padding-left: 12px; position: relative; }
.faq-text::before { content: ""; position: absolute; left: 0; top: 0.9em; transform: translateY(-50%); width: 4px; height: 4px; border-radius: 50%; background: var(--el-color-primary-light-5); }

.download-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(260px, 1fr)); gap: 12px; }
.download-item { display: flex; align-items: center; justify-content: space-between; padding: 12px; border: 1px solid var(--el-border-color); border-radius: 6px; background: var(--el-bg-color); }
.file-meta { display: flex; flex-direction: column; gap: 4px; }
.file-name { font-weight: 500; display: flex; align-items: center; gap: 8px; }
.file-badge { flex-shrink: 0; }
.file-desc { color: var(--el-text-color-secondary); font-size: 12px; }

/* 折叠面板交互反馈（仅文字变色） */
:deep(.el-collapse) { border: none; }
:deep(.el-collapse-item) { border: 1px solid var(--el-border-color); border-radius: 6px; margin: 0 0 8px; overflow: hidden; transition: transform 0.2s ease, box-shadow 0.2s ease; }
:deep(.el-collapse-item:hover) { transform: translateY(-2px); box-shadow: 0 4px 14px rgba(0, 0, 0, 0.08); }
:deep(.el-collapse-item__header) {
  padding: 10px 12px;
  font-weight: 500;
  transition: color 0.15s ease;
  position: relative;
  cursor: pointer;
}
:deep(.el-collapse-item__header:hover) { color: var(--el-color-primary); }
:deep(.el-collapse-item__header:hover .sec-index) { color: var(--el-color-primary); }
/* 取消左侧竖条装饰 */
:deep(.el-collapse-item.is-active .el-collapse-item__header) { color: var(--el-color-primary); }
:deep(.el-collapse-item.is-active .el-collapse-item__header .sec-index) { color: var(--el-color-primary); }
/* 保持箭头颜色不变 */
:deep(.el-collapse-item__header .el-collapse-item__arrow) { color: var(--el-text-color-regular); }
:deep(.el-collapse-item.is-active .el-collapse-item__header .el-collapse-item__arrow) { color: var(--el-text-color-regular); }
:deep(.el-collapse-item__wrap) { border-top: 1px solid var(--el-border-color-light); overflow: hidden; }
:deep(.el-collapse-item__content) { padding: 8px 0 6px; transition: padding 0.2s ease; }
</style>

