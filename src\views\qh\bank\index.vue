<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams">
      <el-form-item label="试卷名称" prop="paperName">
        <el-input
            v-model="queryParams.paperName"
            clearable
            placeholder="请输入试卷名称"
            style="width: 180px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="题库来源" prop="sourcePaper">
        <el-input
            v-model="queryParams.sourcePaper"
            clearable
            placeholder="请输入题库来源"
            style="width: 180px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="组卷方式" prop="paperStyle">
        <el-select v-model="queryParams.paperStyle" clearable placeholder="请选择组卷方式" style="width: 180px">
          <el-option
              v-for="dict in sys_qh_zu_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--            v-hasPermi="['qh:bank:export']"-->
<!--            icon="Download"-->
<!--            plain-->
<!--            type="warning"-->
<!--            @click="handleExport"-->
<!--        >导出-->
<!--        </el-button>-->
<!--      </el-col>-->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="paperList" border @selection-change="handleSelectionChange">
<!--      <el-table-column align="center" type="selection" width="55"/>-->
      <el-table-column align="center" label="试卷名称" prop="paperName" width="320"/>
      <el-table-column align="center" label="组卷方式" prop="paperStyle" width="100">
        <template #default="scope">
          <dict-tag :options="sys_qh_zu_type" :value="scope.row.paperStyle"/>
        </template>
      </el-table-column>
      <el-table-column align="center" label="题目数量" prop="num" width="80"/>
      <el-table-column align="center" label="总分" prop="score" width="80"/>
      <el-table-column :show-overflow-tooltip="true" align="center" label="题库来源" prop="sourcePaper"/>
      <el-table-column align="center" label="创建人" prop="createBy"/>
      <el-table-column align="center" label="创建时间" prop="createTime"/>
      <el-table-column align="center" label="最后更新时间" prop="updateTime"/>
      <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="220">
        <template #default="scope">
          <el-tooltip content="查看" placement="top">
            <el-button v-hasPermi="['qh:bank:query']" icon="View" link type="primary"
                       @click="handleView(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="修改" placement="top">
            <el-button v-hasPermi="['qh:bank:edit']" icon="Edit" link type="primary"
                       @click="handleUpdate(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button v-hasPermi="['qh:bank:remove']" icon="Delete" link type="danger"
                       @click="handleDelete(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="导出" placement="top">
            <el-button icon="Download" link type="warning" @click="handleExport(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="平行组卷" placement="top">
            <el-button icon="Operation" link type="primary"
                       @click="handleParallel(scope.row)"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total > 0"
        v-model:limit="queryParams.pageSize"
        v-model:page="queryParams.pageNum"
        :total="total"
        @pagination="getList"
    />

    <!-- 添加或修改定时任务对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body width="820px">
      <el-form ref="paperRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="试卷名称" prop="paperName">
              <el-input v-model="form.paperName" placeholder="请输入试卷名称"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导出报告对话框 -->
    <el-dialog v-model="exportOpen" title="试卷导出" append-to-body width="500px">
      <el-form ref="exportFormRef" :model="exportForm" label-width="100px">
        <el-form-item label="文件格式" prop="exportType">
          <el-radio-group v-model="exportForm.exportType">
            <el-radio label="PDF">PDF格式</el-radio>
            <el-radio label="WORD">Word格式</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="导出内容">
          <el-checkbox-group v-model="exportForm.contentOptions">
            <el-checkbox label="includeQuestions" checked disabled>试卷题目</el-checkbox>
            <el-checkbox label="includeAnswers">试卷答案</el-checkbox>
            <el-checkbox label="includeAnalysis">试卷解析</el-checkbox>
          </el-checkbox-group>
          <div class="form-tip">注：试卷题目为必选项</div>
        </el-form-item>

        <el-divider content-position="left">格式设置</el-divider>

        <el-form-item label="页面大小">
          <el-select v-model="exportForm.formatOptions.pageSize" style="width: 120px">
            <el-option label="A4" value="A4"></el-option>
            <el-option label="A3" value="A3"></el-option>
            <el-option label="Letter" value="Letter"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="字体大小">
          <el-input-number v-model="exportForm.formatOptions.fontSize" :min="8" :max="24" style="width: 120px"></el-input-number>
        </el-form-item>

        <el-form-item label="其他选项">
          <el-checkbox v-model="exportForm.formatOptions.showQuestionNumbers">显示题目序号</el-checkbox>
          <el-checkbox v-model="exportForm.formatOptions.showScores">显示分数</el-checkbox>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmExport">确 定</el-button>
          <el-button @click="exportOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 试卷查看抽屉 -->
    <el-drawer
            v-model="drawerVisible"
            :close-on-click-modal="true"
            :destroy-on-close="true"
            :with-header="true"
            direction="rtl"
            size="53%"
    >
      <template #header>
        <div class="drawer-header">
          <span class="drawer-title">查看试卷</span>
        </div>
      </template>
      <div class="drawer-content">
        <div v-if="drawerVisible && selectedPaperId" class="drawer-loading-container">
          <paper-detail-component
                  :key="selectedPaperId"
                  :paper-id="selectedPaperId"
                  :paper-name="currentPaperName"
                  :flag="'ZJ'"
                  @close="closeDrawer"
          />
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script name="Bank" setup>
import {listPaper, getPaper, delPaper, addPaper, updatePaper, pingXingPaper, exportPaperReport } from "@/api/qh/paper";
import { getCurrentInstance, reactive, ref, toRefs } from 'vue';
import { useRouter } from 'vue-router';
import PaperDetailComponent from '../test/components/PaperDetail.vue';
import { saveAs } from 'file-saver';
// 前端导出逻辑已移除
// import { codeToText, regionData } from "element-china-area-data";

const router = useRouter();
const {proxy} = getCurrentInstance();
const {sys_qh_zu_type} = proxy.useDict("sys_qh_zu_type");

const paperList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const openView = ref(false);
const openCron = ref(false);
const expression = ref("");

// 抽屉相关状态
const drawerVisible = ref(false);
const selectedPaperId = ref(null);
const currentPaperName = ref('');

// 导出相关状态
const exportOpen = ref(false);
const exportForm = reactive({
  paperId: '',
  exportType: 'PDF',
  contentOptions: ['includeQuestions'],
  formatOptions: {
    pageSize: 'A4',
    fontSize: 12,
    showQuestionNumbers: true,
    showScores: true,
    margin: 20,
    lineSpacing: 1.5
  },
  flag: 'ZJ'
});

const data = reactive({
  form: {
    id: '',
    paperName: '',
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    paperName: '',
    paperStyle: '',
    sourcePaper: '',
    flag: 'ZJ'
  },
  rules: {
    // paperName: [{required: true, message: "试卷名称不能为空", trigger: "blur"}],
  }
});

const {queryParams, form, rules} = toRefs(data);

// 地区格式化方法已删除，不再需要

/** 查询试卷列表 */
function getList() {
  loading.value = true;
  listPaper(queryParams.value).then(response => {
    paperList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(error => {
    loading.value = false;
  });
}

/** 任务组名字典翻译 */
// function jobGroupFormat(row, column) {
//   return proxy.selectDictLabel(sys_qh_zu_type.value, row.paperStyle);
// }

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: '',
    paperName: '',
    paperType: '',
  };
  proxy.resetForm("paperRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  // 保存当前flag值
  const currentFlag = queryParams.value.flag;
  proxy.resetForm("queryRef");
  // 恢复flag值
  queryParams.value.flag = currentFlag;
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value;
  getPaper(id).then(response => {
    // 只保留允许修改的字段
    const data = response.data;
    form.value = {
      id: data.id,
      paperName: data.paperName,
    };
    open.value = true;
    title.value = "修改试卷";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["paperRef"].validate(valid => {
    if (valid) {
      if (form.value.id !== '') {
        updatePaper(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addPaper(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const id = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除【' + row.paperName + '】?').then(function () {
    return delPaper(id, 'ZJ');
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport(row) {
  exportForm.paperId = row.id;
  exportOpen.value = true;
}

/** 查看按钮操作 */
function handleView(row) {
  // 验证必要参数
  if (!row || !row.id) {
    proxy.$modal.msgError('试卷信息不完整，无法查看');
    return;
  }
  
  // 设置状态
  selectedPaperId.value = row.id;
  currentPaperName.value = row.paperName;
  drawerVisible.value = true;
  // 延迟关闭加载提示，给组件一些时间初始化
  setTimeout(() => {
    proxy.$modal.closeLoading();
  }, 500);
}

/** 关闭抽屉 */
function closeDrawer() {
  drawerVisible.value = false;
  // 重置selectedPaperId，确保下次打开抽屉时会重新触发组件挂载
  selectedPaperId.value = null;
  currentPaperName.value = '';
}

/** 确认导出（改为后端导出） */
function confirmExport() {
  // 验证至少选择了试卷题目
  if (!exportForm.contentOptions.includes('includeQuestions')) {
    proxy.$modal.msgError('试卷题目为必选项');
    return;
  }

  const loading = proxy.$loading({
    lock: true,
    text: '正在导出，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  // 构建导出数据
  const exportData = {
    paperId: exportForm.paperId,
    exportType: exportForm.exportType,
    contentOptions: {
      includeQuestions: exportForm.contentOptions.includes('includeQuestions'),
      includeAnswers: exportForm.contentOptions.includes('includeAnswers'),
      includeAnalysis: exportForm.contentOptions.includes('includeAnalysis')
    },
    formatOptions: exportForm.formatOptions,
    flag: exportForm.flag
  };

  exportPaperReport(exportData)
    .then((blob) => {
      loading.close();
      if (!(blob instanceof Blob)) {
        proxy.$modal.msgError('导出失败：返回数据格式不正确');
        return;
      }
      const ext = exportForm.exportType === 'PDF' ? 'pdf' : 'docx';
      const fileName = `试卷导出_${new Date().getTime()}.${ext}`;
      saveAs(new Blob([blob]), fileName);
      exportOpen.value = false;
      proxy.$modal.msgSuccess('导出成功');
    })
    .catch((error) => {
      loading.close();
      proxy.$modal.msgError(error?.message || '导出失败，请联系管理员');
    });
}

// 前端导出实现已移除，改为调用后端导出接口

/** 格式化试卷内容 */
function formatPaperContent(paperData) {
  let content = '试卷题目\n\n';
  
  // 按题型分组
  const groups = {};
  paperData.forEach(question => {
    const type = question.questionType;
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(question);
  });
  
  // 按题型顺序输出
  Object.keys(groups).forEach(type => {
    const questions = groups[type];
    content += `题型${type}：\n`;
    questions.forEach((question, index) => {
      content += `${index + 1}. ${question.context || '题目内容'}\n`;
      if (question.score) {
        content += `   分值：${question.score}分\n`;
      }
      content += '\n';
    });
  });
  
  return content;
}

/** 平行组卷按钮操作 */
function handleParallel(row) {
  proxy.$modal.confirm('确定要基于此试卷进行平行组卷？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    const loading = proxy.$loading({
      lock: true,
      text: '平行组卷中，请稍候...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    pingXingPaper(row.id, 'ZJ').then(response => {
      loading.close();
      if (response.code === 200) {
        proxy.$modal.msgSuccess('平行组卷成功');
        getList();
      } else {
        proxy.$modal.msgError(response.msg || '平行组卷失败');
      }
    }).catch(error => {
      loading.close();
      proxy.$modal.msgError('平行组卷失败');
    });
  }).catch(() => {
    // 用户取消操作
  });
}

getList();
</script>

<style scoped>
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.drawer-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.drawer-content {
  padding: 10px;
  height: 100%;
}

.drawer-loading-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 抽屉加载状态样式 */
.drawer-loading-container .paper-detail-container {
  flex: 1;
  overflow-y: auto;
}

/* 导出对话框样式 */
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.el-checkbox-group .el-checkbox {
  display: block;
  margin-bottom: 8px;
}

.el-checkbox-group .el-checkbox:last-child {
  margin-bottom: 0;
}

.el-form-item__content .el-checkbox {
  margin-right: 20px;
}

/* 确保加载状态在抽屉中正确显示 */
.drawer-loading-container .loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background-color: #fafafa;
  border-radius: 8px;
  margin: 20px;
}
</style>
