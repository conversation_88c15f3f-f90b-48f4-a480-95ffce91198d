<template>
  <div class="app-container">
    <!-- 头部标题和操作区 -->
    <div class="header-bar">
      <h3 class="title">试卷列表</h3>
      <div class="actions">
        <el-button size="small" type="primary" @click="handleManualPaper">手动组卷</el-button>
        <el-button size="small" type="success" @click="handleAutoPaper">自动组卷</el-button>
      </div>
    </div>

    <!-- 试卷列表页 -->
    <div v-loading="pageLoading">
      <!-- 试卷列表 -->
      <div class="paper-container">
            <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="80px" size="default">
              <el-form-item label="试卷名称" prop="paperName">
                <el-input
                        v-model="queryParams.paperName"
                        clearable
                        style="width: 180px"
                        placeholder="请输入试卷名称"
                        @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="试卷类型" prop="paperType">
                <el-select v-model="queryParams.paperType" clearable placeholder="请选择试卷类型" style="width: 180px">
                  <el-option
                          v-for="dict in sys_qh_paper_type"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="地区" prop="region">
                <el-cascader
                        v-model="queryParams.region"
                        :options="regionData"
                        :props="{ value: 'value', label: 'label', children: 'children' }"
                        clearable
                        placeholder="请选择地区"
                        style="width: 180px"
                        :show-all-levels="false"
                />
              </el-form-item>
              <el-form-item label="年份" prop="pyear" style="width: 270px">
                <el-date-picker
                        v-model="queryParams.pyear"
                        type="year"
                        value-format="YYYY"
                        placeholder="请选择年份"
                        style="width: 180px"
                />
              </el-form-item>
              <el-form-item>
                <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <!-- 表格容器 -->
            <div class="table-container">
              <el-table
                      v-loading="loading"
                      :data="paperList"
                      border
                      height="500"
                      :max-height="tableMaxHeight"
                      @selection-change="handleSelectionChange">
                <!--              <el-table-column align="center" type="selection" width="55"/>-->
                <el-table-column align="center" label="试卷名称" prop="paperName" width="350"/>
                <el-table-column align="center" label="试卷类型" prop="paperType" width="100">
                  <template #default="scope">
                    <span>{{ sys_qh_paper_type.find(item => item.value === scope.row.paperType)?.label || '-' }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="地区" prop="region" width="200">
                  <template #default="scope">
                    <span v-if="scope.row.region">{{ formatRegion(scope.row.region) }}</span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="年份" prop="pyear" width="100">
                  <template #default="scope">
                    <span v-if="scope.row.pyear">{{ scope.row.pyear }}</span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="创建人" prop="createBy"/>
                <el-table-column align="center" label="创建时间" prop="createTime"/>
                <el-table-column align="center" label="最后更新时间" prop="updateTime"/>
                <el-table-column align="center" class-name="small-padding fixed-width" label="操作" fixed="right">
                  <template #default="scope">
                    <el-tooltip content="查看" placement="top">
                      <el-button icon="View" link type="primary" @click="handleView(scope.row)"></el-button>
                    </el-tooltip>
                    <el-tooltip content="删除" placement="top">
                      <el-button icon="Delete" link type="danger" @click="handleDelete(scope.row)"></el-button>
                    </el-tooltip>
                    <el-tooltip content="平行组卷" placement="top">
                      <el-button icon="Operation" link type="primary" @click="handlePingXing(scope.row)"></el-button>
                    </el-tooltip>
                    <el-tooltip content="导出" placement="top">
                      <el-button icon="Download" link type="warning" @click="handleExport(scope.row)"></el-button>
                    </el-tooltip>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <pagination 
                    v-show="total > 0" 
                    v-model:limit="queryParams.pageSize" 
                    v-model:page="queryParams.pageNum"
                    :total="total"
                    style="text-align: center; margin-top: 0px" 
                    @pagination="getList"
            />
      </div>

      <!-- 导出试卷对话框 -->
      <el-dialog v-model="exportOpen" title="试卷导出" append-to-body width="500px">
        <el-form ref="exportFormRef" :model="exportForm" label-width="100px">
          <el-form-item label="文件格式" prop="exportType">
            <el-radio-group v-model="exportForm.exportType">
              <el-radio label="PDF">PDF格式</el-radio>
              <el-radio label="WORD">Word格式</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="导出内容">
            <el-checkbox-group v-model="exportForm.contentOptions">
              <el-checkbox label="includeQuestions" checked disabled>试卷题目</el-checkbox>
              <el-checkbox label="includeAnswers">试卷答案</el-checkbox>
              <el-checkbox label="includeAnalysis">试卷解析</el-checkbox>
            </el-checkbox-group>
            <div class="form-tip">注：试卷题目为必选项</div>
          </el-form-item>

          <el-divider content-position="left">格式设置</el-divider>

          <el-form-item label="页面大小">
            <el-select v-model="exportForm.formatOptions.pageSize" style="width: 120px">
              <el-option label="A4" value="A4"></el-option>
              <el-option label="A3" value="A3"></el-option>
              <el-option label="Letter" value="Letter"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="字体大小">
            <el-input-number v-model="exportForm.formatOptions.fontSize" :min="8" :max="24" style="width: 120px"></el-input-number>
          </el-form-item>

          <el-form-item label="其他选项">
            <el-checkbox v-model="exportForm.formatOptions.showQuestionNumbers">显示题目序号</el-checkbox>
            <el-checkbox v-model="exportForm.formatOptions.showScores">显示分数</el-checkbox>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="confirmExport">确 定</el-button>
            <el-button @click="exportOpen = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>

    <!-- 试卷查看抽屉 -->
    <el-drawer
            v-model="drawerVisible"
            :close-on-click-modal="true"
            :destroy-on-close="false"
            :with-header="true"
            direction="rtl"
            size="53%"
    >
      <template #header>
        <div class="drawer-header">
          <span class="drawer-title">查看试卷</span>
        </div>
      </template>
      <div class="drawer-content">
        <paper-detail-component
                v-if="drawerVisible && selectedPaperId"
                :key="selectedPaperId"
                :paper-id="selectedPaperId"
                :paper-name="currentPaperName"
                :flag="'SJ'"
                @close="closeDrawer"
        />
      </div>
    </el-drawer>
  </div>
</template>

<script name="TestPaper" setup>
  import {onMounted, reactive, ref, getCurrentInstance, nextTick, onBeforeUnmount} from 'vue';
  import {useRouter} from 'vue-router';
  import {delPaper, listPaper, updatePaper, pingXingPaper, exportPaperReport} from "@/api/qh/paper";
  import {ElMessage, ElMessageBox, ElLoading} from 'element-plus';
  import PaperDetailComponent from './components/PaperDetail.vue';
  import { saveAs } from 'file-saver';
 import {codeToText, regionData} from 'element-china-area-data';

  const router = useRouter();
  const {proxy} = getCurrentInstance();
  const {sys_qh_paper_type} = proxy.useDict("sys_qh_paper_type");
  // 不再使用数据字典，改用地区组件
  // 遮罩层
  const loading = ref(true);
  // 页面初始加载
  const pageLoading = ref(true);
  // 选中数组
  const ids = ref([]);
  // 非单个禁用
  const single = ref(true);
  // 非多个禁用
  const multiple = ref(true);
  // 显示搜索条件
  const showSearch = ref(true);
  // 总条数
  const total = ref(0);
  // 试卷表格数据
  const paperList = ref([]);
  // 弹出层标题
  const title = ref("");
  // 是否显示弹出层
  const open = ref(false);
  // 导出对话框
  let exportOpen = ref(false);
  // 导出表单
  const exportForm = reactive({
    paperId: '',
    exportType: 'PDF',
    contentOptions: ['includeQuestions'],
    formatOptions: {
      pageSize: 'A4',
      fontSize: 12,
      showQuestionNumbers: true,
      showScores: true,
      margin: 20,
      lineSpacing: 1.5
    },
    flag: 'SJ'
  });
  // 查询参数
  const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    paperName: '',
    paperType: '',
    region: null,
    pyear: null,
    flag: 'SJ',
  });
  // 表单参数
  const form = reactive({
    id: '',
    paperName: '',
    sourcePaper: ''
  });
  // 表单校验
  const rules = {
    paperName: [
      {required: true, message: "试卷名称不能为空", trigger: "blur"}
    ],
  };

  // 试题篮Id
  const selectedPaperId = ref(null);
  // 树搜索过滤文本
  // const filterText = ref('');
  // 抽屉可见性
  const drawerVisible = ref(false);
  // 当前试卷名称
  const currentPaperName = ref('');

  // 表格最大高度控制
  const tableMaxHeight = ref(500);

  // 地区格式化方法，兼容数组和字符串格式
  const formatRegion = (regionData) => {
    // 处理空值情况
    if (!regionData) {
      return '';
    }
    // 统一将输入转换为数组（确保至少3个元素）
    let regionCodes = [];
    if (Array.isArray(regionData)) {
      // 数组格式：截取前3个元素（确保是省、市、区三级）
      regionCodes = regionData.slice(0, 3);
    } else if (typeof regionData === 'string') {
      // 字符串格式：用逗号分割后取前3个元素
      regionCodes = regionData.split(',').map(code => code.trim()).filter(code => code).slice(0, 3);
    }
    // 确保有3个编码（不足则补空，避免数组长度不够）
    while (regionCodes.length < 3) {
      regionCodes.push('');
    }
    // 解析三级编码（省、市、区）
    const province = codeToText[regionCodes[0]] || ''; // 第一级：省
    const city = codeToText[regionCodes[1]] || '';     // 第二级：市
    const district = codeToText[regionCodes[2]] || ''; // 第三级：区

    // 处理特殊情况：如果市编码无效，但省编码有效（如直辖市）
    const validCity = city || province;
    // 拼接结果（过滤空值）
    const regionNames = [province, validCity, district].filter(name => name);

    // 特殊处理：如果只有一个有效名称（如直辖市）
    if (regionNames.length === 1) {
      return regionNames[0];
    }
    // 正常拼接为"省/市/区"
    return regionNames.join(' / ');
  };

  // 重新计算表格高度的函数
  const calcTableHeight = () => {
    // 根据屏幕高度动态计算表格高度，留出足够空间给分页组件和其他元素
    const windowHeight = window.innerHeight;
    // 预留空间：顶部标题栏、搜索栏、分页组件、边距等
    tableMaxHeight.value = windowHeight - 320;
    // 设置最小高度，避免在小屏幕上表格太小
    if (tableMaxHeight.value < 400) {
      tableMaxHeight.value = 400;
    }
  };

  // 窗口大小变化时重新计算表格高度
  const handleResize = () => {
    calcTableHeight();
  };

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    // 保存当前flag值
    const currentFlag = queryParams.flag;
    proxy.resetForm("queryForm");
    // 恢复flag值并重置单选字段
    queryParams.flag = currentFlag;
    queryParams.region = null;
    queryParams.pyear = null;
    queryParams.paperType = '';
    handleQuery();
  }

  /** 查询试卷列表 */
  function getList() {
    loading.value = true;
    
    // 处理查询参数，将地区数组转换为字符串
    const params = { ...queryParams };
    if (params.region && Array.isArray(params.region)) {
      params.region = params.region.join(',');
    }
    
    listPaper(params).then(response => {
      paperList.value = response.rows;
      total.value = response.total;
    }).finally(() => {
      loading.value = false;
      pageLoading.value = false;
    });
  }

  /** 打开抽屉查看试卷 */
  function handleView(row) {
    console.log('查看按钮被点击，试卷ID:', row.id, '试卷名称:', row.paperName);
    if (!row || !row.id) {
      ElMessage.error('试卷信息不完整，无法查看');
      return;
    }
    
    // 先设置数据，再打开抽屉
    selectedPaperId.value = row.id;
    currentPaperName.value = row.paperName;
    drawerVisible.value = true;
    
    console.log('抽屉状态已设置，selectedPaperId:', selectedPaperId.value);
  }

  /** 关闭抽屉 */
  function closeDrawer() {
    console.log('关闭抽屉，重置状态');
    drawerVisible.value = false;
    // 延迟重置，确保组件完全销毁后再重置状态
    setTimeout(() => {
      selectedPaperId.value = null;
      currentPaperName.value = '';
      console.log('状态已重置');
    }, 100);
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
  }

  /** 开启自动组卷 */
  function handleAutoPaper() {
    router.push('/qh/paper/index');
  }

  /** 开启手动组卷 */
  function handleManualPaper() {
    router.push('/qh/questionBank');
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    ElMessageBox.confirm('此操作会同步删除试卷所有题目，是否确认删除【"' + row.paperName + '"】?', "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }).then(function () {
      return delPaper(_ids, 'SJ');
    }).then(() => {
      getList();
      ElMessage.success("删除成功");
    }).catch(() => {
    });
  }

  /** 导出按钮操作 */
  function handleExport(row) {
    // if (multiple.value) {
    //   ElMessage.warning("请选择一个试卷进行导出");
    //   return;
    // }

    exportForm.paperId = row.id;
    exportOpen.value = true;
  }

  /** 确认导出（改为后端导出） */
  function confirmExport() {
    // 验证至少选择了试卷题目
    if (!exportForm.contentOptions.includes('includeQuestions')) {
      ElMessage.error('试卷题目为必选项');
      return;
    }

    const loading = ElLoading.service({
      lock: true,
      text: '正在导出，请稍候...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 构建导出数据
    const exportData = {
      paperId: exportForm.paperId,
      exportType: exportForm.exportType,
      contentOptions: {
        includeQuestions: exportForm.contentOptions.includes('includeQuestions'),
        includeAnswers: exportForm.contentOptions.includes('includeAnswers'),
        includeAnalysis: exportForm.contentOptions.includes('includeAnalysis')
      },
      formatOptions: exportForm.formatOptions,
      flag: exportForm.flag
    };

    exportPaperReport(exportData)
      .then((blob) => {
        loading.close();
        if (!(blob instanceof Blob)) {
          ElMessage.error('导出失败：返回数据格式不正确');
          return;
        }
        const ext = exportForm.exportType === 'PDF' ? 'pdf' : 'docx';
        const fileName = `试卷导出_${new Date().getTime()}.${ext}`;
        saveAs(new Blob([blob]), fileName);
        exportOpen.value = false;
        ElMessage.success('导出成功');
      })
      .catch((error) => {
        loading.close();
        ElMessage.error(error?.message || '导出失败，请联系管理员');
      });
  }



  // 前端导出实现已移除，改为调用后端导出接口

  /** 平行组卷操作 */
  function handlePingXing(row) {
    ElMessageBox.confirm('确定要基于此试卷进行平行组卷？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    }).then(() => {
      const loading = ElLoading.service({
        lock: true,
        text: '平行组卷中，请稍候...',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      pingXingPaper(row.id, 'SJ').then(response => {
        loading.close();
        if (response.code === 200) {
          ElMessage.success('平行组卷成功');
          getList();
        } else {
          ElMessage.error(response.msg || '平行组卷失败');
        }
      }).catch(error => {
        loading.close();
      });
    }).catch(() => {});
  }

  /** 新增按钮操作 */
  function handleAdd() {
    // 新增逻辑
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    title.value = "修改试卷";
    open.value = true;
    // 复制行数据到表单对象
    Object.assign(form, row);
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
      if (valid) {
        if (form.id) {
          // 如果有ID，则是更新操作
          updatePaper(form).then(response => {
            if (response.code === 200) {
              ElMessage.success("修改成功");
              open.value = false;
              getList();
            } else {
              ElMessage.error(response.msg);
            }
          });
        }
      }
    });
  }

  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }

  /** 重置数据表单 */
  function reset() {
    Object.keys(form).forEach(key => {
      form[key] = undefined;
    });
    proxy.resetForm("formRef");
  }

  onMounted(() => {
    getList();
    // loadTreeData(categoryTreeData);
    // 初始计算表格高度
    nextTick(() => {
      calcTableHeight();
      // 添加窗口大小变化监听
      window.addEventListener('resize', handleResize);
    });
  });

  // 组件销毁前移除事件监听
  onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize);
  });
</script>

<style scoped>
  .app-container {
    padding: 25px;
  }

  .header-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    margin-bottom: 15px;
  }

  .title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin: 0;
  }

  .actions {
    display: flex;
    gap: 10px;
  }

  .paper-container {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 21, 41, 0.12);
    display: flex;
    flex-direction: column;
    margin: 20px 0;
  }



  .table-container {
    margin-bottom: 20px;
    padding: 10px;
    background-color: #fff;
    border-radius: 6px;
    border: 1px solid #e9ecef;
  }

  /* 导出对话框样式 */
  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
  }

  .el-checkbox-group .el-checkbox {
    display: block;
    margin-bottom: 8px;
  }

  .el-checkbox-group .el-checkbox:last-child {
    margin-bottom: 0;
  }

  .el-form-item__content .el-checkbox {
    margin-right: 20px;
  }

  .drawer-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .drawer-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
  }

  .drawer-content {
    padding: 10px;
    height: 100%;
  }

</style>

