import { defineStore } from 'pinia';
import { getChatMessagePage, markAllChatMessagesAsRead, deleteChatMessage, getUnreadMessageCount } from '@/api/qh/chat';
import { getChatWebSocket, isGlobalChatWebSocketActive } from '@/utils/websocket';
import useUserStore from '@/store/modules/user';

export const useChatStore = defineStore('chat', {
  state: () => ({
    chatMessages: [],
    unreadChatCount: 0,
    page: { current: 1, pageSize: 10, hasMore: true },
    currentUserOid: null,
    webSocketConnected: false,
    onlineUsers: new Set(), // 在线用户集合
    isLoading: false // 添加加载状态
  }),
  actions: {
    // 检查WebSocket连接状态
    checkWebSocketConnection() {
      this.webSocketConnected = isGlobalChatWebSocketActive();
      return this.webSocketConnected;
    },

    // 处理WebSocket消息
    handleWebSocketMessage(data) {
      // 兼容服务端包体外包一层 data/payload/body/message 的情况
      const envelope = data || {};
      const inner = envelope.data || envelope.payload || envelope.body || (typeof envelope.message === 'object' ? envelope.message : null);
      const merged = inner && typeof inner === 'object'
        ? { ...inner, type: envelope.type || envelope.event || envelope.msgType || inner.type }
        : envelope;

      const normalizedType = ((merged && (merged.type || merged.event || merged.msgType)) || '').toString().toUpperCase();
      switch (normalizedType) {
        case 'CHAT':
        case 'MESSAGE':
        case 'WS_CHAT':
          this.handleNewChatMessage(merged);
          break;
        case 'SYSTEM':
          this.handleSystemMessage(merged);
          break;
        case 'USERCONNECTIONSTATUS':
        case 'USER_CONNECTION_STATUS':
          this.handleUserConnectionStatus(merged);
          break;
        case 'MESSAGEREAD':
        case 'MESSAGE_READ':
          this.handleMessageRead(merged);
          break;
        default:
          console.log('未处理的WebSocket消息:', envelope);
      }
    },

    // 处理新的聊天消息
    handleNewChatMessage(messageData) {
      console.log('收到新聊天消息:', messageData);

      const loginUserId = String(useUserStore().id || '');
      const currentPeerId = String(this.currentUserOid || '');

      const messageSenderId = messageData.senderId
        || messageData.sender_id
        || messageData.senderOid
        || messageData.fromId
        || messageData.fromUserId;

      const messageReceiverId = messageData.receiverId
        || messageData.receiveTarget
        || messageData.receiverOid
        || messageData.toId
        || messageData.toUserId;

      const messageContent = messageData.content
        || messageData.message
        || messageData.text
        || '';

      const messageCreateTime = messageData.createTime
        || messageData.create_time
        || messageData.timestamp
        || Date.now();

      const isImage = Number(messageData.isImage || messageData.image || 0);

      const hasLoginUserId = !!loginUserId;
      const isForLoginUser = hasLoginUserId
        ? (String(messageSenderId ?? '') === loginUserId || String(messageReceiverId ?? '') === loginUserId)
        : true; // 若暂未取到登录用户ID，先不过滤，避免漏掉实时消息

      const belongsToCurrentConversation = currentPeerId && (
        (hasLoginUserId && String(messageSenderId ?? '') === loginUserId && String(messageReceiverId ?? '') === currentPeerId)
        || (hasLoginUserId && String(messageSenderId ?? '') === currentPeerId && String(messageReceiverId ?? '') === loginUserId)
        // 当还没拿到登录ID时，退化为“只要一端是当前会话对端”即可
        || (!hasLoginUserId && (String(messageSenderId ?? '') === currentPeerId || String(messageReceiverId ?? '') === currentPeerId))
      );

      console.log('登录用户ID:', loginUserId, '当前会话对端ID:', currentPeerId);
      console.log('消息发送者:', messageSenderId, '消息接收者:', messageReceiverId);
      console.log('是否与登录用户相关:', isForLoginUser, '是否属于当前会话:', belongsToCurrentConversation);

      // 标准化消息数据格式
      const normalizedMessage = {
        oid: messageData.messageId || messageData.oid || Date.now().toString(),
        content: messageContent,
        senderId: messageSenderId,
        senderName: messageData.senderName || messageData.fromUserName || messageData.fromName,
        receiverId: messageReceiverId,
        receiveTarget: messageReceiverId,
        createTime: messageCreateTime,
        isImage,
        quoteOid: messageData.quoteOid,
        quoteContent: messageData.quoteContent,
        chatReadStatus: 0
      };

      // 使用统一的消息添加方法
      const added = this.addMessageToCurrentChat(normalizedMessage, 'WebSocketPush');

      // 如果消息被添加到当前会话，更新未读计数
      if (added) {
        this.updateUnreadCount();
      }

      // 无论消息是否添加到当前会话，都需要更新左侧用户列表
      this.updateUserListOnNewMessage(normalizedMessage);
    },

    // 处理系统消息
    handleSystemMessage(messageData) {
      console.log('收到系统消息:', messageData);
      // 可以在这里显示系统通知
    },

    // 处理用户连接状态变更
    handleUserConnectionStatus(data) {
      if (data.connected) {
        this.onlineUsers.add(data.userId);
      } else {
        this.onlineUsers.delete(data.userId);
      }
    },

    // 处理消息已读通知
    handleMessageRead(data) {
      // 更新消息的已读状态
      this.chatMessages.forEach(message => {
        if (message.messageId === data.messageId) {
          message.chatReadStatus = 1;
        }
      });
    },

    // 更新未读消息数量
    async updateUnreadCount() {
      try {
        const response = await getUnreadMessageCount();
        if (response.code === 200) {
          this.unreadChatCount = response.data || 0;
        }
      } catch (error) {
        console.error('获取未读消息数量失败:', error);
      }
    },

    resetPage() {
      this.page = { current: 1, pageSize: 10, hasMore: true };
    },
    setCurrentUser(userOid) {
      this.currentUserOid = userOid;
    },
    async fetchChatMessage(userOid, refresh = false) {
      try {
        // 防止重复提交
        if (this.isLoading) {
          console.log('正在加载中，跳过重复请求');
          return;
        }
        
        this.isLoading = true;
        
        if (refresh) {
          this.resetPage();
          this.chatMessages = [];
        }
        if (!this.page.hasMore) {
          this.isLoading = false;
          return;
        }

        const { current, pageSize } = this.page;
        const params = {
          userOid,
          pageNum: current,
          pageSize
        };

        console.log('获取聊天消息，参数:', params);
        const resp = await getChatMessagePage(params);

        // 适配更多API响应格式：records/list/rows 以及顶层 list/rows
        let list = [];
        if (resp && resp.code === 200) {
          list = resp.data?.records || resp.data?.list || resp.data?.rows || [];
        } else if (resp) {
          list = resp.list || resp.rows || [];
        }

        if (refresh) {
          this.chatMessages = list;
        } else {
          this.chatMessages = [...this.chatMessages, ...list];
        }

        // 按时间排序
        this.chatMessages = [...this.chatMessages].sort((a, b) => {
          const ta = Number(a.createTime || 0);
          const tb = Number(b.createTime || 0);
          return ta - tb;
        });

        this.page.hasMore = list.length === pageSize;
        this.page.current += 1;
        this.currentUserOid = userOid;

        return resp;
      } catch (error) {
        console.error('获取聊天消息失败:', error);
        throw error;
      } finally {
        this.isLoading = false;
      }
    },
    async markWsChatAsRead(userOid) {
      this.chatMessages = this.chatMessages.map((msg) => ({
        ...msg,
        chatReadStatus: msg.senderId === userOid ? 1 : msg.chatReadStatus
      }));
      await markAllChatMessagesAsRead(userOid);
    },
    async markAllChatAsRead(userOid) {
      this.chatMessages = this.chatMessages.map((msg) => ({ ...msg, chatReadStatus: 1 }));
      await markAllChatMessagesAsRead(userOid);
    },
    async deleteChatMessage(userOid) {
      await deleteChatMessage(userOid);
      this.chatMessages = [];
      this.resetPage();
    },
    // 统一的消息添加方法，带上下文判断
    addMessageToCurrentChat(message, source = 'unknown') {
      const loginUserId = String(useUserStore().id || '');
      const currentPeerId = String(this.currentUserOid || '');

      const messageSenderId = String(message.senderId || message.sender_id || message.senderOid || '');
      const messageReceiverId = String(message.receiverId || message.receiveTarget || message.receiverOid || '');

      console.log(`${source} - 消息上下文检查:`, {
        loginUserId,
        currentPeerId,
        messageSenderId,
        messageReceiverId,
        messageContent: message.content
      });

      // 检查消息是否属于当前会话
      const belongsToCurrentConversation = currentPeerId && (
        // 情况1：我发给当前选中用户的消息
        (loginUserId && messageSenderId === loginUserId && messageReceiverId === currentPeerId)
        ||
        // 情况2：当前选中用户发给我的消息
        (loginUserId && messageSenderId === currentPeerId && messageReceiverId === loginUserId)
      );

      if (belongsToCurrentConversation) {
        console.log(`${source} - 消息属于当前会话，添加到聊天记录`);

        // 检查是否已存在相同消息（避免重复）
        const existingMessage = this.chatMessages.find(msg =>
          msg.oid === message.oid ||
          msg.messageId === message.messageId ||
          (msg.content === message.content && Math.abs(Number(msg.createTime) - Number(message.createTime)) < 1000)
        );

        if (!existingMessage) {
          this.chatMessages.push(message);

          // 按时间排序
          this.chatMessages.sort((a, b) => {
            const ta = Number(a.createTime || 0);
            const tb = Number(b.createTime || 0);
            return ta - tb;
          });

          console.log(`${source} - 消息已添加，当前消息数量:`, this.chatMessages.length);
          return true;
        } else {
          console.log(`${source} - 消息已存在，跳过添加`);
          return false;
        }
      } else {
        console.log(`${source} - 消息不属于当前会话，跳过添加:`, {
          reason: !currentPeerId ? '未选中聊天对象' : '消息发送者/接收者与当前会话不匹配',
          currentChat: `${loginUserId} <-> ${currentPeerId}`,
          messageChat: `${messageSenderId} -> ${messageReceiverId}`
        });
        return false;
      }
    },

    // 兼容旧的方法名，内部调用新的统一方法
    addOneChatMsgDataByWS(message) {
      return this.addMessageToCurrentChat(message, 'LocalSend');
    },
    setUnread(count) {
      this.unreadChatCount = Math.max(0, count);
    },
    incUnread() {
      this.unreadChatCount = Math.max(0, this.unreadChatCount + 1);
    },
    decUnread() {
      this.unreadChatCount = Math.max(0, this.unreadChatCount - 1);
    },

    // 收到新消息时更新左侧用户列表
    updateUserListOnNewMessage(message) {
      // 这个方法需要在ChatPanel组件中实现，因为userList是组件的本地状态
      // 我们通过事件总线或者直接调用组件方法来实现
      console.log('收到新消息，需要更新用户列表:', message);

      // 发出事件，让ChatPanel组件监听并更新用户列表
      if (typeof window !== 'undefined' && window.dispatchEvent) {
        const event = new CustomEvent('chatMessageReceived', {
          detail: {
            senderId: message.senderId,
            receiverId: message.receiverId,
            content: message.content,
            createTime: message.createTime
          }
        });
        window.dispatchEvent(event);
      }
    }
  }
});

