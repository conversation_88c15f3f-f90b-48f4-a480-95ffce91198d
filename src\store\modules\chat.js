import { defineStore } from 'pinia';
import { getChatMessagePage, markAllChatMessagesAsRead, deleteChatMessage, getUnreadMessageCount } from '@/api/qh/chat';
import { getChatWebSocket, isGlobalChatWebSocketActive } from '@/utils/websocket';
import useUserStore from '@/store/modules/user';

export const useChatStore = defineStore('chat', {
  state: () => ({
    chatMessages: [],
    unreadChatCount: 0,
    page: { current: 1, pageSize: 10, hasMore: true },
    currentUserOid: null,
    webSocketConnected: false,
    onlineUsers: new Set(), // 在线用户集合
    isLoading: false // 添加加载状态
  }),
  actions: {
    // 检查WebSocket连接状态
    checkWebSocketConnection() {
      this.webSocketConnected = isGlobalChatWebSocketActive();
      return this.webSocketConnected;
    },

    // 处理WebSocket消息
    handleWebSocketMessage(data) {
      // 兼容服务端包体外包一层 data/payload/body/message 的情况
      const envelope = data || {};
      const inner = envelope.data || envelope.payload || envelope.body || (typeof envelope.message === 'object' ? envelope.message : null);
      const merged = inner && typeof inner === 'object'
        ? { ...inner, type: envelope.type || envelope.event || envelope.msgType || inner.type }
        : envelope;

      const normalizedType = ((merged && (merged.type || merged.event || merged.msgType)) || '').toString().toUpperCase();
      switch (normalizedType) {
        case 'CHAT':
        case 'MESSAGE':
        case 'WS_CHAT':
          this.handleNewChatMessage(merged);
          break;
        case 'SYSTEM':
          this.handleSystemMessage(merged);
          break;
        case 'USERCONNECTIONSTATUS':
        case 'USER_CONNECTION_STATUS':
          this.handleUserConnectionStatus(merged);
          break;
        case 'MESSAGEREAD':
        case 'MESSAGE_READ':
          this.handleMessageRead(merged);
          break;
        default:
          console.log('未处理的WebSocket消息:', envelope);
      }
    },

    // 处理新的聊天消息
    handleNewChatMessage(messageData) {
      console.log('收到新聊天消息:', messageData);

      const loginUserId = String(useUserStore().id || '');
      const currentPeerId = String(this.currentUserOid || '');

      const messageSenderId = messageData.senderId
        || messageData.sender_id
        || messageData.senderOid
        || messageData.fromId
        || messageData.fromUserId;

      const messageReceiverId = messageData.receiverId
        || messageData.receiveTarget
        || messageData.receiverOid
        || messageData.toId
        || messageData.toUserId;

      const messageContent = messageData.content
        || messageData.message
        || messageData.text
        || '';

      const messageCreateTime = messageData.createTime
        || messageData.create_time
        || messageData.timestamp
        || Date.now();

      const isImage = Number(messageData.isImage || messageData.image || 0);

      const hasLoginUserId = !!loginUserId;
      const isForLoginUser = hasLoginUserId
        ? (String(messageSenderId ?? '') === loginUserId || String(messageReceiverId ?? '') === loginUserId)
        : true; // 若暂未取到登录用户ID，先不过滤，避免漏掉实时消息

      const belongsToCurrentConversation = currentPeerId && (
        (hasLoginUserId && String(messageSenderId ?? '') === loginUserId && String(messageReceiverId ?? '') === currentPeerId)
        || (hasLoginUserId && String(messageSenderId ?? '') === currentPeerId && String(messageReceiverId ?? '') === loginUserId)
        // 当还没拿到登录ID时，退化为“只要一端是当前会话对端”即可
        || (!hasLoginUserId && (String(messageSenderId ?? '') === currentPeerId || String(messageReceiverId ?? '') === currentPeerId))
      );

      console.log('登录用户ID:', loginUserId, '当前会话对端ID:', currentPeerId);
      console.log('消息发送者:', messageSenderId, '消息接收者:', messageReceiverId);
      console.log('是否与登录用户相关:', isForLoginUser, '是否属于当前会话:', belongsToCurrentConversation);

      if (isForLoginUser && belongsToCurrentConversation) {
        const newMessage = {
          oid: messageData.messageId || messageData.oid || Date.now().toString(),
          content: messageContent,
          senderId: messageSenderId,
          senderName: messageData.senderName || messageData.fromUserName || messageData.fromName,
          receiveTarget: messageReceiverId,
          createTime: messageCreateTime,
          isImage,
          quoteOid: messageData.quoteOid,
          quoteContent: messageData.quoteContent,
          chatReadStatus: 0
        };

        console.log('准备添加新消息:', newMessage);

        const existingMessage = this.chatMessages.find(msg =>
          msg.oid === newMessage.oid ||
          (msg.content === newMessage.content && Math.abs(Number(msg.createTime) - Number(newMessage.createTime)) < 1000)
        );

        if (!existingMessage) {
          this.chatMessages.push(newMessage);
          console.log('消息已添加，当前消息数量:', this.chatMessages.length);

          this.chatMessages.sort((a, b) => {
            const ta = Number(a.createTime || 0);
            const tb = Number(b.createTime || 0);
            return ta - tb;
          });
        } else {
          console.log('消息已存在，跳过添加');
        }
      } else {
        console.log('消息不涉及当前用户，跳过处理');
      }

      this.updateUnreadCount();
    },

    // 处理系统消息
    handleSystemMessage(messageData) {
      console.log('收到系统消息:', messageData);
      // 可以在这里显示系统通知
    },

    // 处理用户连接状态变更
    handleUserConnectionStatus(data) {
      if (data.connected) {
        this.onlineUsers.add(data.userId);
      } else {
        this.onlineUsers.delete(data.userId);
      }
    },

    // 处理消息已读通知
    handleMessageRead(data) {
      // 更新消息的已读状态
      this.chatMessages.forEach(message => {
        if (message.messageId === data.messageId) {
          message.chatReadStatus = 1;
        }
      });
    },

    // 更新未读消息数量
    async updateUnreadCount() {
      try {
        const response = await getUnreadMessageCount();
        if (response.code === 200) {
          this.unreadChatCount = response.data || 0;
        }
      } catch (error) {
        console.error('获取未读消息数量失败:', error);
      }
    },

    resetPage() {
      this.page = { current: 1, pageSize: 10, hasMore: true };
    },
    setCurrentUser(userOid) {
      this.currentUserOid = userOid;
    },
    async fetchChatMessage(userOid, refresh = false) {
      try {
        // 防止重复提交
        if (this.isLoading) {
          console.log('正在加载中，跳过重复请求');
          return;
        }
        
        this.isLoading = true;
        
        if (refresh) {
          this.resetPage();
          this.chatMessages = [];
        }
        if (!this.page.hasMore) {
          this.isLoading = false;
          return;
        }

        const { current, pageSize } = this.page;
        const params = {
          userOid,
          pageNum: current,
          pageSize
        };

        console.log('获取聊天消息，参数:', params);
        const resp = await getChatMessagePage(params);

        // 适配更多API响应格式：records/list/rows 以及顶层 list/rows
        let list = [];
        if (resp && resp.code === 200) {
          list = resp.data?.records || resp.data?.list || resp.data?.rows || [];
        } else if (resp) {
          list = resp.list || resp.rows || [];
        }

        if (refresh) {
          this.chatMessages = list;
        } else {
          this.chatMessages = [...this.chatMessages, ...list];
        }

        // 按时间排序
        this.chatMessages = [...this.chatMessages].sort((a, b) => {
          const ta = Number(a.createTime || 0);
          const tb = Number(b.createTime || 0);
          return ta - tb;
        });

        this.page.hasMore = list.length === pageSize;
        this.page.current += 1;
        this.currentUserOid = userOid;

        return resp;
      } catch (error) {
        console.error('获取聊天消息失败:', error);
        throw error;
      } finally {
        this.isLoading = false;
      }
    },
    async markWsChatAsRead(userOid) {
      this.chatMessages = this.chatMessages.map((msg) => ({
        ...msg,
        chatReadStatus: msg.senderId === userOid ? 1 : msg.chatReadStatus
      }));
      await markAllChatMessagesAsRead(userOid);
    },
    async markAllChatAsRead(userOid) {
      this.chatMessages = this.chatMessages.map((msg) => ({ ...msg, chatReadStatus: 1 }));
      await markAllChatMessagesAsRead(userOid);
    },
    async deleteChatMessage(userOid) {
      await deleteChatMessage(userOid);
      this.chatMessages = [];
      this.resetPage();
    },
    addOneChatMsgDataByWS(message) {
      this.chatMessages.push(message);
    },
    setUnread(count) {
      this.unreadChatCount = Math.max(0, count);
    },
    incUnread() {
      this.unreadChatCount = Math.max(0, this.unreadChatCount + 1);
    },
    decUnread() {
      this.unreadChatCount = Math.max(0, this.unreadChatCount - 1);
    }
  }
});

