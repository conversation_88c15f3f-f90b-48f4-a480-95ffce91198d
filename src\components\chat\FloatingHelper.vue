<template>
  <div class="floating-helper">
    <el-badge :value="totalUnread" :max="99">
      <el-tooltip content="智能助手" placement="left">
        <el-button type="primary" circle @click="visible = true">
          <el-icon><ChatDotRound /></el-icon>
        </el-button>
      </el-tooltip>
    </el-badge>

    <el-drawer v-model="visible" :title="'智能助手'" direction="rtl" :size="drawerSize" :with-header="true" destroy-on-close>
      <div class="floating-helper__content">
        <el-tabs v-model="activeTab" class="full-tabs">
          <el-tab-pane label="即时对话" name="chat">
            <ChatPanel />
          </el-tab-pane>
          <el-tab-pane label="功能指南" name="help">
            <HelpPanel :key="helpKey" />
          </el-tab-pane>
          <el-tab-pane label="更新日志" name="changelog">
            <ChangelogPanel />
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-drawer>
  </div>
  
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import { ChatDotRound } from '@element-plus/icons-vue';
import { useChatStore } from '@/store/modules/chat';
import ChatPanel from './ChatPanel.vue';
import HelpPanel from './HelpPanel.vue';
import ChangelogPanel from './ChangelogPanel.vue';

const chatStore = useChatStore();
const totalUnread = computed(() => chatStore.unreadChatCount);

const visible = ref(false);
const activeTab = ref('chat');
const helpKey = ref(0);

const screenWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 1200);
const onResize = () => {
  if (typeof window !== 'undefined') screenWidth.value = window.innerWidth;
};

onMounted(() => {
  if (typeof window !== 'undefined') window.addEventListener('resize', onResize);
});

onBeforeUnmount(() => {
  if (typeof window !== 'undefined') window.removeEventListener('resize', onResize);
});

const drawerSize = computed(() => (screenWidth.value <= 768 ? '100%' : '880px'));

watch(activeTab, (val, old) => {
  if (old === 'help' && val !== 'help') {
    // 离开功能指南时重置其内部展开状态
    helpKey.value += 1;
  }
});

watch(() => visible.value, (val) => {
  if (!val) {
    // 抽屉关闭时重置为初始状态
    activeTab.value = 'chat';
  }
});
</script>

<style scoped>
.floating-helper {
  position: fixed;
  right: 24px;
  bottom: 24px;
  z-index: 1000;
}

.floating-helper__content {
  height: 100%;
  padding-top: 4px;
  border-top: 1px solid var(--el-border-color);
}

.helper-pane {
  color: var(--el-text-color-regular);
}

/* 让 Tab 内容拉满抽屉高度 */
:deep(.full-tabs) { height: 100%; display: flex; flex-direction: column; }
:deep(.full-tabs > .el-tabs__content) { flex: 1; height: 100%; }
:deep(.full-tabs .el-tab-pane) { height: 100%; }

/* 微调标题与分割线间距 */
:deep(.el-drawer__header) { margin-bottom: 4px; }
</style>

