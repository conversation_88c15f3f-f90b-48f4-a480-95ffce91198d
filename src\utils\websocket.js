import { getToken } from '@/utils/auth';

/**
 * WebSocket聊天客户端
 * 支持Token验证、自动重连、心跳检测
 */
class ChatWebSocket {
  constructor(options = {}) {
    // 动态获取WebSocket URL
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.hostname;
    
    // 根据环境配置端口和路径
    let port, path;
    if (process.env.NODE_ENV === 'development') {
      // 开发环境：使用vite代理，端口与前端一致
      port = window.location.port || '8200'; // 如果port为空，使用默认端口8200
      path = '/domino/chat';
    } else {
      // 生产环境：通过nginx代理
      port = window.location.port || '8200'; // 如果port为空，使用默认端口8200
      path = '/domino/chat';
    }
    
    const defaultUrl = `${protocol}//${host}:${port}${path}`;

    this.options = {
      url: defaultUrl,
      heartbeatInterval: 30000, // 30秒心跳
      reconnectInterval: 3000,   // 3秒重连间隔
      maxReconnectAttempts: 5,   // 最大重连次数
      ...options
    };
    
    this.websocket = null;
    this.reconnectAttempts = 0;
    this.heartbeatTimer = null;
    this.isConnected = false;
    this.isManualClose = false;
    
    // 事件回调
    this.onConnected = null;
    this.onDisconnected = null;
    this.onMessage = null;
    this.onError = null;
  }

  /**
   * 建立WebSocket连接
   */
  connect() {
    try {
      const token = getToken();
      if (!token) {
        console.error('WebSocket连接失败：未找到用户Token');
        return false;
      }

      // 构建WebSocket URL，通过查询参数传递Token
      const wsUrl = `${this.options.url}?token=${encodeURIComponent(token)}`;
      
      // 检查是否已经连接
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        return true;
      }
      
      this.websocket = new WebSocket(wsUrl);
      this.setupEventHandlers();
      
      return true;
    } catch (error) {
      console.error('WebSocket连接失败:', error);
      this.handleError(error);
      return false;
    }
  }

  /**
   * 设置WebSocket事件处理器
   */
  setupEventHandlers() {
    this.websocket.onopen = (event) => {
      console.log('WebSocket连接已建立，状态:', this.websocket.readyState);
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.startHeartbeat();
      
      if (this.onConnected) {
        this.onConnected(event);
      }
    };

    this.websocket.onmessage = (event) => {
      try {
        console.log('收到WebSocket原始消息:', event.data);
        const data = JSON.parse(event.data);
        this.handleMessage(data);
      } catch (error) {
        console.error('WebSocket消息解析失败:', error, '原始数据:', event.data);
      }
    };

    this.websocket.onclose = (event) => {
      console.log('WebSocket连接已关闭:', event.code, event.reason, '手动关闭:', this.isManualClose);
      this.isConnected = false;
      this.stopHeartbeat();
      
      if (this.onDisconnected) {
        this.onDisconnected(event);
      }

      // 如果不是手动关闭，尝试重连
      if (!this.isManualClose && this.reconnectAttempts < this.options.maxReconnectAttempts) {
        this.reconnect();
      }
    };

    this.websocket.onerror = (error) => {
      console.error('WebSocket错误:', error);
      this.handleError(error);
    };
  }

  /**
   * 处理接收到的消息
   */
  handleMessage(data) {
    console.log('收到WebSocket消息:', data);
    
    switch (data.type) {
      case 'connection':
        console.log('WebSocket连接成功:', data.message);
        break;
      case 'heartbeat':
        console.log('收到心跳响应');
        break;
      case 'CHAT':
        console.log('收到聊天消息:', data);
        break;
      case 'SYSTEM':
        console.log('收到系统消息:', data);
        break;
      case 'userConnectionStatus':
        console.log('用户连接状态变更:', data);
        break;
      case 'messageRead':
        console.log('消息已读通知:', data);
        break;
      default:
        console.log('未知消息类型:', data);
    }

    if (this.onMessage) {
      this.onMessage(data);
    }
  }

  /**
   * 发送消息
   */
  send(message) {
    if (!this.isConnected || !this.websocket) {
      console.warn('WebSocket未连接，无法发送消息');
      return false;
    }

    try {
      const messageStr = typeof message === 'string' ? message : JSON.stringify(message);
      this.websocket.send(messageStr);
      return true;
    } catch (error) {
      console.error('发送WebSocket消息失败:', error);
      return false;
    }
  }

  /**
   * 发送聊天消息
   */
  sendChatMessage(content, receiverId) {
    return this.send({
      type: 'chat',
      content,
      receiverId,
      timestamp: Date.now()
    });
  }

  /**
   * 发送心跳
   */
  sendHeartbeat() {
    return this.send({
      type: 'heartbeat',
      timestamp: Date.now()
    });
  }

  /**
   * 开始心跳检测
   */
  startHeartbeat() {
    this.stopHeartbeat(); // 先停止之前的心跳
    
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.sendHeartbeat();
      }
    }, this.options.heartbeatInterval);
  }

  /**
   * 停止心跳检测
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * 重连
   */
  reconnect() {
    if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      console.error('WebSocket重连次数已达上限，停止重连');
      return;
    }

    this.reconnectAttempts++;
    console.log(`WebSocket重连中... (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})`);

    // 延迟重连，避免频繁重连
    const delay = this.options.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1);
    console.log(`将在 ${delay}ms 后重连`);
    
    setTimeout(() => {
      console.log('开始重连WebSocket');
      this.connect();
    }, delay);
  }

  /**
   * 手动关闭连接
   */
  close() {
    this.isManualClose = true;
    this.stopHeartbeat();
    
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
    
    this.isConnected = false;
  }

  /**
   * 处理错误
   */
  handleError(error) {
    if (this.onError) {
      this.onError(error);
    }
  }

  /**
   * 设置事件回调
   */
  on(event, callback) {
    switch (event) {
      case 'connected':
        this.onConnected = callback;
        break;
      case 'disconnected':
        this.onDisconnected = callback;
        break;
      case 'message':
        this.onMessage = callback;
        break;
      case 'error':
        this.onError = callback;
        break;
      default:
        console.warn('未知的事件类型:', event);
    }
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      readyState: this.websocket ? this.websocket.readyState : WebSocket.CLOSED
    };
  }
}

// 创建全局WebSocket实例
let chatWebSocket = null;

/**
 * 获取WebSocket实例
 */
export function getChatWebSocket() {
  if (!chatWebSocket) {
    chatWebSocket = new ChatWebSocket();
  }
  return chatWebSocket;
}

/**
 * 初始化WebSocket连接
 */
export function initChatWebSocket(options = {}) {
  chatWebSocket = new ChatWebSocket(options);
  return chatWebSocket;
}

/**
 * 关闭WebSocket连接
 */
export function closeChatWebSocket() {
  if (chatWebSocket) {
    chatWebSocket.close();
    chatWebSocket = null;
  }
}

export default ChatWebSocket;
