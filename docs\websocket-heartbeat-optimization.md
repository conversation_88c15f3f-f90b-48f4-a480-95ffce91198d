# WebSocket心跳检测优化方案

## 问题分析

### 原始实现问题
- 心跳检测通过WebSocket发送消息实现
- 每30秒发送一次WebSocket心跳消息
- 增加了WebSocket通道的负担
- 心跳消息与业务消息混合，增加处理复杂度

### 优化需求
- 使用HTTP API `/system/message/status` 进行心跳检测
- 减少WebSocket通道的消息量
- 分离心跳检测与业务消息处理

## 解决方案

### 1. 修改心跳检测机制

#### 原始实现：
```javascript
// 通过WebSocket发送心跳消息
sendHeartbeat() {
  return this.send({
    type: 'heartbeat',
    timestamp: Date.now()
  });
}
```

#### 优化后实现：
```javascript
// 使用API调用进行心跳检测
async sendHeartbeat() {
  try {
    const response = await checkMessageStatus();
    if (response && response.code === 200) {
      console.log('心跳检测成功:', response.data);
      return true;
    } else {
      console.warn('心跳检测响应异常:', response);
      return false;
    }
  } catch (error) {
    console.error('心跳检测失败:', error);
    return false;
  }
}
```

### 2. 调整心跳间隔

#### 配置优化：
```javascript
this.options = {
  heartbeatInterval: 60000,  // 从30秒调整到60秒
  reconnectInterval: 3000,   // 保持3秒重连间隔
  maxReconnectAttempts: 5,   // 保持最大重连次数
};
```

#### 优化理由：
- API调用比WebSocket消息更可靠
- 60秒间隔足够检测连接状态
- 减少服务器负担

### 3. 新增API接口

#### 添加状态检测API：
```javascript
// 检查消息服务状态（用于心跳检测）
export function checkMessageStatus() {
    return request({
        url: '/system/message/status',
        method: 'get'
    });
}
```

### 4. 移除WebSocket心跳消息处理

#### 清理消息处理逻辑：
```javascript
// 移除心跳消息的处理分支
switch (data.type) {
  case 'connection':
    console.log('WebSocket连接成功:', data.message);
    break;
  // 移除了 case 'heartbeat' 分支
  case 'CHAT':
    console.log('收到聊天消息:', data);
    break;
  // ...其他业务消息处理
}
```

## 优化效果

### 1. 性能提升
- **减少WebSocket消息量：** 心跳不再占用WebSocket通道
- **降低服务器负担：** 心跳检测独立于消息推送
- **提高消息处理效率：** 业务消息处理更纯净

### 2. 可靠性提升
- **独立的健康检查：** API调用可以检测整个消息服务的健康状态
- **更好的错误处理：** HTTP状态码提供更详细的错误信息
- **分离关注点：** 连接保活与消息传输分离

### 3. 维护性提升
- **清晰的职责分工：** WebSocket专注消息传输，HTTP API负责状态检测
- **更好的调试体验：** 心跳检测日志独立，便于排查问题
- **配置灵活性：** 可以独立调整心跳间隔和重连策略

## 测试验证

### 1. 心跳检测测试
访问测试页面 `/qh/websocket-test/index`：
- 点击"测试心跳检测"按钮
- 观察API调用是否成功
- 检查响应时间和数据

### 2. 长期连接测试
- 保持WebSocket连接1小时以上
- 观察心跳检测是否正常工作
- 检查连接是否保持稳定

### 3. 网络异常测试
- 模拟网络中断
- 观察心跳检测的失败处理
- 验证重连机制是否正常

## 配置建议

### 生产环境配置：
```javascript
{
  heartbeatInterval: 60000,  // 60秒心跳间隔
  reconnectInterval: 5000,   // 5秒重连间隔
  maxReconnectAttempts: 3,   // 最大重连3次
}
```

### 开发环境配置：
```javascript
{
  heartbeatInterval: 30000,  // 30秒心跳间隔（便于调试）
  reconnectInterval: 2000,   // 2秒重连间隔
  maxReconnectAttempts: 10,  // 更多重连次数（便于开发）
}
```

## 注意事项

1. **API可用性：** 确保 `/system/message/status` 接口在后端已实现
2. **错误处理：** 心跳失败时的处理策略需要根据业务需求调整
3. **监控告警：** 建议添加心跳失败的监控和告警机制
4. **性能监控：** 定期检查心跳检测的响应时间和成功率
