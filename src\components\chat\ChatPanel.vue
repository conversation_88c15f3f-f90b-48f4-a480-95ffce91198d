<template>
  <div class="chat-container">
    <div class="user-list">
      <div class="search-input">
        <div class="search-row">
          <el-input v-model="keyword" placeholder="搜索用户" clearable @clear="loadUsers" @keyup.enter="loadUsers">
            <template #suffix>
              <el-icon class="search-icon" @click="loadUsers"><Search /></el-icon>
            </template>
          </el-input>
          <el-button class="add-btn" @click="openAddUser">发起对话</el-button>
        </div>
      </div>
      <div class="user-items">
        <div v-for="user in userList" :key="user.oid" :class="['user-item', { active: currentUser && currentUser.oid === user.oid }]" @click="selectUser(user)">
          <div class="avatar-wrapper">
            <el-avatar :size="28" :icon="UserFilled" />
          </div>
          <div class="user-info">
            <div class="user-name">{{ user.name }}</div>
            <div class="user-status">{{ user.lastMessage || '暂无消息' }}</div>
          </div>
          <el-badge v-if="user.unread" :value="user.unread" class="user-unread" />
        </div>
      </div>
      <div class="clear-list" v-if="userList && userList.length">
        <el-popconfirm title="确认清空对话列表？清空将删除所有聊天信息，且无法恢复。" @confirm="handleClearList">
          <template #reference>
            <el-button type="danger" text>清空对话列表</el-button>
          </template>
        </el-popconfirm>
      </div>
    </div>

    <div class="chat-main">
      <div v-if="!currentUser" class="chat-overlay">
        <div class="overlay-content">
          <p>请选择用户对话</p>
          <el-button type="primary" plain @click="openAddUser">发起对话</el-button>
        </div>
      </div>

      <template v-else>
        <div class="chat-header">
          <div class="title">
            <strong>{{ currentUser.name }}</strong>
            <span v-if="currentUser.phone" class="sub">- {{ currentUser.phone }}</span>
          </div>
          <div class="ops">
            <!-- WebSocket连接状态显示 -->
            <el-tag :type="chatStore.webSocketConnected ? 'success' : 'danger'" size="small" class="mr-8">
              {{ chatStore.webSocketConnected ? '已连接' : '未连接' }}
            </el-tag>
            <!-- 测试按钮 -->
            <el-button size="small" @click="testWebSocketMessage" class="mr-8">测试消息</el-button>
            <el-button size="small" :disabled="!currentUser" @click="markAllRead">标记已读</el-button>
            <el-popconfirm title="确认清空与该用户的聊天记录？" @confirm="clearDialog">
              <template #reference>
                <el-button size="small" type="danger" :disabled="!currentUser">清空对话</el-button>
              </template>
            </el-popconfirm>
          </div>
        </div>

        <el-scrollbar ref="scrollRef" class="chat-messages" @scroll="onScroll">
          <div v-if="chatStore.page.hasMore" class="load-more">
            <el-button text @click="loadMore">加载更多</el-button>
          </div>
          <!-- 调试信息 -->
          <div style="padding: 8px; background: #f0f0f0; font-size: 12px; color: #666;">
            当前用户: {{ userInfo.oid }} | 聊天对象: {{ currentUserOid }} | 消息数量: {{ chatStore.chatMessages.length }} | 
            WebSocket: {{ chatStore.webSocketConnected ? '已连接' : '未连接' }} | 
            加载状态: {{ chatStore.isLoading ? '加载中' : '空闲' }}
          </div>
          <div class="msg-list">
            <div v-for="(msg, idx) in chatStore.chatMessages" :key="idx" class="msg-item" :class="{ me: isMe(msg) }">
              <el-avatar :size="28" :icon="UserFilled" />
              <div class="msg-content">
                <div class="msg-bubble">
                  <div v-if="msg.quoteContent" class="msg-quote">{{ msg.quoteContent }}</div>
                  <template v-if="msg.isImage === 1">
                    <img :src="msg.content" class="msg-image" @click="previewImage(msg.content)" />
                  </template>
                  <template v-else>
                    <div class="content">{{ msg.content }}</div>
                  </template>
                  <div class="msg-actions">
                    <span v-if="isNotMe(msg) && msg.isImage !== 1" class="reply-link" @click="handleReply(msg)">回复</span>
                  </div>
                </div>
                <div class="meta-below" :class="{ me: isMe(msg) }">
                  <span>{{ formatRelativeTime(msg.createTime) }}</span>
                  <template v-if="isMe(msg)">
                    <span class="ml-8" :class="{ read: msg.chatReadStatus === 1 }">{{ msg.chatReadStatus === 1 ? '对方已读' : '对方未读' }}</span>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </el-scrollbar>

        <div class="chat-input">
          <div v-if="replyToMessage" class="reply-preview">
            <span class="label">回复：</span>
            <span class="reply-text">{{ replyToMessage.content }}</span>
            <el-button text type="primary" @click="replyToMessage = null">取消</el-button>
          </div>
          <el-input v-model="text" :rows="2" type="textarea" placeholder="输入消息..." @keyup.enter.exact="send" @paste="onPaste" />
          <div class="mt-8 flex-end">
            <el-button type="primary" :disabled="!text.trim()" @click="send">发送</el-button>
          </div>
        </div>
      </template>
    </div>

    <!-- 发起对话二级抽屉：基于智能助手抽屉左侧半屏，点击遮罩关闭 -->
    <transition name="slide-left">
      <div v-if="addVisible" class="add-mask" @click="addVisible = false">
        <div class="add-drawer" @click.stop>
          <div class="add-header">
            <span class="title">发起对话</span>
          </div>
          <div class="add-body">
            <div class="add-search">
              <el-input
                v-model="userKeyword"
                placeholder="输入姓名/手机号"
                clearable
                @clear="fetchUserTable"
                @keyup.enter="fetchUserTable"
              >
                <template #suffix>
                  <el-icon class="search-icon" @click="fetchUserTable"><Search /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="user-grid">
              <div v-for="user in userTable" :key="user.oid" class="user-card" @click="addOrSelectUser(user)">
                <el-avatar :size="32" :icon="UserFilled" />
                <div class="user-card-meta">
                  <div class="name">{{ user.name }}</div>
                  <div class="phone">{{ user.phone }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="add-footer">
            <div class="page-info">第 {{ page }} / {{ totalPages }} 页 · 共 {{ total }} 条</div>
            <el-pagination
              layout="sizes, prev, pager, next"
              :total="total"
              :current-page="page"
              :page-size="pageSize"
              :page-sizes="[10, 18, 20, 30, 50]"
              @size-change="onPageSizeChange"
              @current-change="onPageChange"
            />
          </div>
        </div>
      </div>
    </transition>

    <!-- 图片预览 -->
    <el-image-viewer v-if="imagePreviewVisible" :url-list="[imageUrl]" @close="imagePreviewVisible = false" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, onUnmounted } from 'vue';
import { UserFilled, Search } from '@element-plus/icons-vue';
import { useChatStore } from '@/store/modules/chat';
import useUserStore from '@/store/modules/user';
import { getChatUserList, sendChatMessage, getUnreadMessageCount } from '@/api/qh/chat';
import { listUser } from '@/api/system/user';
import { startGlobalChatWebSocket, isGlobalChatWebSocketActive } from '@/utils/websocket';

const chatStore = useChatStore();
const userStore = useUserStore();
// 统一映射当前用户信息，保证始终返回对象，避免模板中访问 userInfo.oid 报错
const userInfo = computed(() => ({
  oid: userStore.id || '',
  name: userStore.name || '',
  phone: userStore.phone || '',
  sex: typeof userStore.sex === 'number' ? userStore.sex : 0,
}));

const userList = ref([]);
const keyword = ref('');
const currentUserOid = computed(() => chatStore.currentUserOid);
const currentUser = computed(() => userList.value.find((u) => u.oid === currentUserOid.value));

const scrollRef = ref();
const text = ref('');
const replyToMessage = ref(null);
const imagePreviewVisible = ref(false);
const imageUrl = ref('');

function isMe(msg) {
  // 兼容不同字段：senderId / sender_id / fromId 等，如后端不同可再扩展
  const sid = msg?.senderId ?? msg?.sender_id ?? msg?.fromId;
  return String(sid || '') === String(userInfo.value.oid || '');
}

function isNotMe(msg) {
  return !isMe(msg);
}

function formatTime(t) {
  if (!t) return '';
  // 兼容时间戳/字符串/Date
  let d;
  if (t instanceof Date) {
    d = t;
  } else if (typeof t === 'number' || (/^\d+$/.test(String(t)))) {
    // 纯数字或number，按时间戳处理
    const num = Number(t);
    d = new Date(num > 1e12 ? num : num * 1); // 这里保留原样，后端若传秒级需自行调整
  } else {
    d = new Date(t);
  }
  if (isNaN(d.getTime())) return '';
  const p = (n) => (n < 10 ? `0${n}` : `${n}`);
  return `${d.getFullYear()}-${p(d.getMonth() + 1)}-${p(d.getDate())} ${p(d.getHours())}:${p(d.getMinutes())}`;
}

function toDate(val) {
  if (!val) return null;
  if (val instanceof Date) return val;
  if (typeof val === 'number' || (/^\d+$/.test(String(val)))) {
    const num = Number(val);
    return new Date(num > 1e12 ? num : num);
  }
  const d = new Date(val);
  return isNaN(d.getTime()) ? null : d;
}

function formatRelativeTime(t) {
  const d = toDate(t);
  if (!d) return '';
  const now = Date.now();
  const ts = d.getTime();
  const diffMs = now - ts;
  const diffMin = Math.floor(diffMs / 60000);
  if (diffMin <= 1) return '刚刚';
  if (diffMin < 60) return `${diffMin}分钟前`;
  // 超过1小时则显示日期+时间（保持和原有 formatTime 一致）
  return formatTime(ts);
}

async function loadUsers() {
  try {
    const resp = await getChatUserList({ keyword: keyword.value, pageNum: 1, pageSize: 50 });
    const list = (resp && resp.data && resp.data.list) || resp?.list || [];
    userList.value = list.map((u) => ({
      oid: u.oid || u.userId || u.id,
      name: u.name || u.userName,
      phone: u.phone,
      lastMessage: u.lastMessage || '',
      unread: u.unread || 0,
      sex: u.sex,
    }));
  } catch (e) {
    userList.value = [];
  }
}

async function selectUser(user) {
  chatStore.setCurrentUser(user.oid);
  await chatStore.fetchChatMessage(user.oid, true);
  await nextTick();
  scrollToBottom();
  // 保存并清零未读
  try { localStorage.setItem('lastChatUser', JSON.stringify({ oid: user.oid, name: user.name })); } catch (e) {}
  if (user.unread && user.unread > 0) {
    await chatStore.markAllChatAsRead(user.oid);
    const found = userList.value.find((u) => u.oid === user.oid);
    if (found) found.unread = 0;
  }
}

function scrollToBottom() {
  const wrap = scrollRef.value && scrollRef.value.wrapRef;
  if (wrap) wrap.scrollTop = wrap.scrollHeight;
}

async function onScroll({ scrollTop }) {
  if (scrollTop <= 0 && currentUserOid.value && chatStore.page.hasMore && !chatStore.isLoading) {
    console.log('触发滚动加载更多消息');
    await chatStore.fetchChatMessage(currentUserOid.value, false);
  }
}

async function loadMore() {
  if (currentUserOid.value && chatStore.page.hasMore && !chatStore.isLoading) {
    console.log('手动加载更多消息');
    await chatStore.fetchChatMessage(currentUserOid.value, false);
  }
}

async function markAllRead() {
  if (!currentUserOid.value) return;
  await chatStore.markAllChatAsRead(currentUserOid.value);
}

async function clearDialog() {
  if (!currentUserOid.value) return;
  await chatStore.deleteChatMessage(currentUserOid.value);
}

async function send() {
  if (!text.value.trim() || !currentUserOid.value) return;

  const msg = {
    type: 'CHAT',
    content: text.value.trim(),
    senderId: userInfo.value && userInfo.value.oid,
    receiverId: currentUserOid.value,
    receiveTarget: currentUserOid.value,
    senderChatPhone: userInfo.value && userInfo.value.phone,
    senderChatSex: userInfo.value && userInfo.value.sex,
    receiveTargetChatPhone: currentUser.value && currentUser.value.phone,
    receiveTargetChatSex: currentUser.value && currentUser.value.sex,
    chatReadStatus: 0,
    createTime: Date.now(),
    ...(replyToMessage.value
      ? { quoteOid: replyToMessage.value.oid, quoteContent: replyToMessage.value.content, quoteTimestamp: replyToMessage.value.createTime }
      : {}),
  };

  try {
    console.log('发送消息:', msg);

    // 先本地追加，立即显示（优化用户体验）
    const localMsg = {
      ...msg,
      oid: 'temp_' + Date.now(),
      messageId: 'temp_' + Date.now(),
      senderName: userInfo.value && userInfo.value.name,
    };
    chatStore.addOneChatMsgDataByWS(localMsg);

    // 立即滚动到底部显示新消息
    await nextTick();
    scrollToBottom();

    // 再提交到后端
    await sendChatMessage(msg);

    // 成功后刷新当前会话，确保与后端数据一致
    if (currentUserOid.value) {
      await chatStore.fetchChatMessage(currentUserOid.value, true);
      // 等待DOM更新后再次滚动到底部
      await nextTick();
      scrollToBottom();
    }

    // 更新左侧最后一条消息显示
    const found = userList.value.find((u) => u.oid === currentUserOid.value);
    if (found) found.lastMessage = msg.content;

    // 清空输入和引用
    text.value = '';
    replyToMessage.value = null;

  } catch (error) {
    console.error('发送消息失败:', error);
    ElMessage.error('发送消息失败');
  }
}

function handleReply(msg) {
  replyToMessage.value = msg;
}

function previewImage(url) {
  imageUrl.value = url;
  imagePreviewVisible.value = true;
}

async function onPaste(e) {
  const items = e.clipboardData && e.clipboardData.items;
  if (!items) return;
  for (let i = 0; i < items.length; i += 1) {
    const item = items[i];
    if (item.type && item.type.indexOf('image') !== -1) {
      e.preventDefault();
      const file = item.getAsFile();
      if (!file) continue;
      if (file.size > 10 * 1024 * 1024) continue;
      const reader = new FileReader();
      reader.onload = async (event) => {
        const base64 = event.target && event.target.result;
        const msg = {
          type: 'CHAT',
          senderId: userInfo.value && userInfo.value.oid,
          senderChatPhone: userInfo.value && userInfo.value.phone,
          senderChatSex: userInfo.value && userInfo.value.sex,
          receiveTarget: currentUser.value && currentUser.value.oid,
          receiveTargetChatPhone: currentUser.value && currentUser.value.phone,
          receiveTargetChatSex: currentUser.value && currentUser.value.sex,
          content: base64,
          isImage: 1,
          chatReadStatus: 0,
          createTime: Date.now(),
        };

        try {
          // 先本地追加，立即显示
          const localMsg = {
            ...msg,
            oid: 'temp_' + Date.now(),
            messageId: 'temp_' + Date.now(),
            senderName: userInfo.value && userInfo.value.name,
          };
          chatStore.addOneChatMsgDataByWS(localMsg);

          // 立即滚动到底部显示新消息
          await nextTick();
          scrollToBottom();

          // 再提交到后端
          await sendChatMessage(msg);

          // 成功后刷新当前会话，确保与后端数据一致
          if (currentUserOid.value) {
            await chatStore.fetchChatMessage(currentUserOid.value, true);
            // 等待DOM更新后再次滚动到底部
            await nextTick();
            scrollToBottom();
          }

          // 更新左侧最后一条消息显示
          const found = userList.value.find((u) => u.oid === (currentUser.value && currentUser.value.oid));
          if (found) found.lastMessage = '[图片]';

        } catch (error) {
          console.error('发送图片消息失败:', error);
          ElMessage.error('发送图片消息失败');
        }
      };
      reader.readAsDataURL(file);
      break;
    }
  }
}

// 发起对话抽屉
const addVisible = ref(false);
const userKeyword = ref('');
const userTable = ref([]);
const page = ref(1);
const pageSize = ref(18);
const total = ref(0);
const totalPages = computed(() => {
  const t = Number(total.value) || 0;
  const ps = Number(pageSize.value) || 1;
  return Math.max(1, Math.ceil(t / ps));
});

function openAddUser() {
  addVisible.value = true;
  fetchUserTable();
}

async function fetchUserTable() {
  try {
    // 调整为调用系统用户列表接口：/system/user/list
    const resp = await listUser({ userName: userKeyword.value, pageNum: page.value, pageSize: pageSize.value });
    const rows = resp?.rows || resp?.data?.rows || [];
    userTable.value = rows.map((u) => ({
      oid: u.userId,
      name: u.nickName || u.userName,
      phone: u.phonenumber || u.phone,
      sex: u.sex,
    }));
    const t = resp?.total ?? resp?.data?.total;
    total.value = typeof t === 'number' ? t : userTable.value.length;
  } catch (e) {
    userTable.value = [];
    total.value = 0;
  }
}

async function fetchUnreadCount() {
  try {
    const cnt = await getUnreadMessageCount();
    const chatUnread = cnt?.chatUnread ?? cnt?.data?.chatUnread ?? cnt?.data ?? 0;
    if (typeof chatStore.setUnread === 'function') chatStore.setUnread(chatUnread);
  } catch (e) {}
}

function onPageChange(p) {
  page.value = p;
  fetchUserTable();
}

function onPageSizeChange(ps) {
  pageSize.value = ps;
  page.value = 1;
  fetchUserTable();
}

function addOrSelectUser(u) {
  const found = userList.value.find((x) => x.oid === u.oid);
  if (!found) {
    const nu = { ...u, unread: 0, lastMessage: '暂无消息' };
    userList.value.unshift(nu);
    selectUser(nu);
  } else {
    selectUser(found);
  }
  addVisible.value = false;
}

function handleClearList() {
  userList.value = [];
  chatStore.setCurrentUser(null);
}

function testWebSocketMessage() {
  const testMsg = {
    type: 'CHAT',
    content: '测试WebSocket消息',
    senderId: userInfo.value && userInfo.value.oid,
    receiverId: currentUserOid.value,
    receiveTarget: currentUserOid.value,
    senderChatPhone: userInfo.value && userInfo.value.phone,
    senderChatSex: userInfo.value && userInfo.value.sex,
    receiveTargetChatPhone: currentUser.value && currentUser.value.phone,
    receiveTargetChatSex: currentUser.value && currentUser.value.sex,
    chatReadStatus: 0,
    createTime: Date.now(),
  };
  console.log('测试消息:', testMsg);
  chatStore.addOneChatMsgDataByWS(testMsg);
  sendChatMessage(testMsg);
}

onMounted(async () => {
  await loadUsers();
  await fetchUnreadCount();

  // 启动全局WebSocket连接（如果尚未启动）
  try {
    console.log('检查并启动全局WebSocket连接');
    if (!isGlobalChatWebSocketActive()) {
      console.log('全局WebSocket连接未激活，开始启动');
      const webSocket = startGlobalChatWebSocket();

      // 设置事件监听器
      webSocket.on('connected', () => {
        console.log('聊天WebSocket连接成功');
        chatStore.webSocketConnected = true;
      });

      webSocket.on('disconnected', () => {
        console.log('聊天WebSocket连接断开');
        chatStore.webSocketConnected = false;
      });

      webSocket.on('message', (data) => {
        chatStore.handleWebSocketMessage(data);
      });

      webSocket.on('error', (error) => {
        console.error('聊天WebSocket错误:', error);
        chatStore.webSocketConnected = false;
      });

      console.log('全局WebSocket连接启动成功');
    } else {
      console.log('全局WebSocket连接已激活，无需重复启动');
      chatStore.webSocketConnected = true;
    }
  } catch (error) {
    console.error('WebSocket连接启动失败:', error);
  }

  // 自动选中上次聊天用户
  const last = localStorage.getItem('lastChatUser');
  if (last) {
    try {
      const u = JSON.parse(last);
      if (u && u.oid) {
        const found = userList.value.find((x) => x.oid === u.oid);
        if (found) {
          await selectUser(found);
        }
      }
    } catch (e) {}
  }
});

// 组件卸载时不关闭WebSocket连接，保持全局连接持久化
onUnmounted(() => {
  console.log('ChatPanel组件卸载，保持WebSocket连接持久化');
  // 不调用 closeChatWebSocket()，让连接保持活跃
});
</script>

<style scoped>
.chat-container { position: relative; display: flex; gap: 12px; height: 100%; }
.user-list { width: 280px; border-right: 1px solid var(--el-border-color); display: flex; flex-direction: column; }
.search-input { padding: 12px; border-bottom: 1px solid var(--el-border-color-light); }
.search-row { display: flex; gap: 8px; }
.search-row :deep(.el-input__suffix) { cursor: pointer; }
.add-btn { white-space: nowrap; }
.user-items { flex: 1; padding: 8px 6px; overflow: auto; }
.user-item { display: flex; gap: 10px; align-items: center; padding: 10px 8px; border-radius: 6px; cursor: pointer; }
.user-item:hover { background: var(--el-fill-color-light); }
.user-item.active { background: var(--el-color-primary-light-9); }
.user-info { flex: 1; min-width: 0; }
.user-name { font-weight: 500; }
.user-status { font-size: 12px; color: var(--el-text-color-secondary); white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }

.chat-main { flex: 1; display: flex; flex-direction: column; height: 100%; position: relative; }
.chat-header { padding: 10px 12px; border-bottom: 1px solid var(--el-border-color); display: flex; justify-content: space-between; align-items: center; }
.chat-header .title { font-size: 14px; }
.chat-header .title .sub { margin-left: 6px; color: var(--el-text-color-secondary); }
.chat-messages { flex: 1; min-height: 0; padding: 6px; background: var(--el-fill-color-lighter); }
.load-more { text-align: center; margin: 6px 0; }
.msg-list { padding: 8px 4px; }
.msg-item { display: flex; align-items: flex-end; margin: 12px 0; gap: 10px; }
.msg-item.me { flex-direction: row-reverse; }
.msg-content { display: flex; flex-direction: column; align-items: flex-start; max-width: 70%; }
.msg-item.me .msg-content { align-items: flex-end; }
.msg-bubble { width: 100%; background: var(--el-fill-color-light); padding: 8px 12px; border-radius: 8px; display: flex; flex-direction: column; align-items: center; }
.msg-item.me .msg-bubble { background: var(--el-color-primary); color: #fff; }
.content { white-space: pre-wrap; word-break: break-word; text-align: center; }
.msg-image { display: block; margin: 0 auto; }
.msg-actions { margin-top: 4px; font-size: 12px; color: var(--el-text-color-secondary); display: flex; justify-content: center; }
.meta-below { margin-top: 4px; font-size: 12px; color: var(--el-text-color-secondary); display: flex; }
.meta-below.me { justify-content: flex-end; }
.meta-below .read { color: var(--el-color-success); }
.chat-input { padding: 8px 8px 16px; border-top: 1px solid var(--el-border-color); margin-top: 14px; }
.flex-end { display: flex; justify-content: flex-end; }
.mt-8 { margin-top: 8px; }
.ml-8 { margin-left: 8px; }
.mr-8 { margin-right: 8px; }

.chat-overlay { position: absolute; inset: 0; display: flex; align-items: center; justify-content: center; color: var(--el-text-color-secondary); background: var(--el-fill-color-lighter); }
.overlay-content { text-align: center; }

.add-body { display: flex; flex-direction: column; gap: 12px; height: 100%; }
.add-search { display: flex; gap: 8px; }
.user-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; overflow: auto; padding-right: 6px; }
.user-card { display: flex; align-items: center; gap: 10px; border: 1px solid var(--el-border-color); border-radius: 6px; padding: 8px; cursor: pointer; }
.user-card:hover { background: var(--el-fill-color-light); }
.user-card-meta .name { font-weight: 500; }
.user-card-meta .phone { font-size: 12px; color: var(--el-text-color-secondary); }
.clear-list { padding: 8px 8px; display: flex; justify-content: center; }

/* 发起对话覆盖抽屉内部 */
.add-mask { position: absolute; inset: 0; z-index: 20; }
.add-drawer { position: absolute; left: 0; top: 0; bottom: 0; width: 50%; background: var(--el-bg-color); border-right: 1px solid var(--el-border-color); box-shadow: 0 8px 24px rgba(0,0,0,0.12); display: flex; flex-direction: column; }
.add-header { display: flex; justify-content: space-between; align-items: center; padding: 10px 12px; border-bottom: 1px solid var(--el-border-color); }
.add-header .title { font-weight: 500; }
.add-body { display: flex; flex-direction: column; gap: 12px; height: 100%; padding: 12px; overflow: hidden; }
.add-footer { display: flex; justify-content: flex-end; padding: 8px 12px; border-top: 1px solid var(--el-border-color); }
.page-info { margin-right: 12px; color: var(--el-text-color-secondary); font-size: 12px; align-self: center; }
.add-search { display: grid; grid-template-columns: 1fr auto; gap: 8px; align-items: center; }
.add-search :deep(.el-input__suffix) { cursor: pointer; }
.search-icon { color: var(--el-text-color-placeholder); }
.search-icon:hover { color: var(--el-color-primary); }
.slide-left-enter-from, .slide-left-leave-to { transform: translateX(-100%); opacity: 1; }
.slide-left-enter-to, .slide-left-leave-from { transform: translateX(0); opacity: 1; }
.slide-left-enter-active, .slide-left-leave-active { transition: transform 0.18s ease; }
</style>

