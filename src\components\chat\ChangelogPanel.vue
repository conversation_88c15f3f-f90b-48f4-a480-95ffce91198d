<template>
  <div class="changelog-container">
    <section v-for="version in versionLogs" :key="version.version" class="version-item">
      <div class="version-header">
        <div class="version-title">
          <h3>{{ version.version }}</h3>
          <el-tag v-if="version.latest" type="success" effect="light">最新版本</el-tag>
        </div>
        <span class="version-date">{{ version.date }}</span>
      </div>
      <div class="version-content">
        <template v-for="(items, type) in version.changes" :key="type">
          <div class="change-type">
            <el-tag size="small" :type="tagType(type)">{{ type }}</el-tag>
          </div>
          <ul class="change-list">
            <li v-for="(item, idx) in items" :key="idx">{{ item }}</li>
          </ul>
        </template>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const versionLogs = ref([
  {
    version: 'v1.2.1',
    date: '2025-07-15',
    latest: true,
    changes: {
      '新功能': ['新增智能助手对话功能', '新增用户在线状态显示', '新增消息未读提醒', '支持多人实时对话'],
      '优化': ['优化界面交互体验', '改进数据加载性能', '优化移动端适配'],
      '修复': ['修复用户列表加载问题', '修复消息发送偶现失败的问题', '修复深色模式下的样式问题'],
    },
  },
  {
    version: 'v1.2.0',
    date: '2025-06-01',
    changes: {
      '新功能': ['新增题库模块', '新增卷库模块', '新增导入导出'],
      '优化': ['优化角色管理', '改进用户权限管理', '优化数据统计首页'],
      '修复': ['新增用户管理', '修复权限验证的漏洞'],
    },
  },
]);

function tagType(type) {
  const map = { '新功能': 'success', '优化': 'primary', '修复': 'warning', '重构': 'danger', '重大更新': 'danger' };
  return map[type] || 'info';
}
</script>

<style scoped>
.changelog-container { padding: 16px; height: 100%; overflow: auto; }
.version-item { margin-bottom: 20px; padding-bottom: 16px; border-bottom: 1px solid var(--el-border-color); }
.version-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }
.version-title { display: flex; align-items: center; gap: 8px; }
.version-title h3 { margin: 0; font-size: 16px; font-weight: 600; }
.version-date { color: var(--el-text-color-secondary); font-size: 12px; }
.change-type { margin: 8px 0; }
.change-list { margin: 0; padding-left: 20px; }
.change-list li { line-height: 1.8; color: var(--el-text-color-regular); }
</style>

