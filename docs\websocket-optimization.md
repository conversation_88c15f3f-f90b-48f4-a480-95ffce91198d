# WebSocket连接优化方案

## 问题分析

### 1. 连接生命周期问题

**原始问题：**
- WebSocket连接与组件生命周期绑定
- 用户切换页面时连接被断开
- 导致消息推送失效

**具体场景：**
```
用户B导航到聊天页面 → onMounted → 建立WebSocket连接
用户B切换到其他页面 → onUnmounted → closeChatWebSocket() → 连接关闭
用户A发送消息 → 用户B无法接收（连接已断开）
```

### 2. 消息上下文判断问题

**原始问题：**
- `addOneChatMsgDataByWS` 方法直接添加消息，无上下文判断
- 可能导致消息显示在错误的聊天窗口中

**具体场景：**
```
用户B正在与用户C聊天
用户A发送消息给用户B
消息被错误地显示在用户B与用户C的聊天窗口中
```

## 解决方案

### 1. 全局持久化连接管理

#### 新增函数（websocket.js）
```javascript
// 启动全局WebSocket连接
export function startGlobalChatWebSocket()

// 检查全局连接是否激活
export function isGlobalChatWebSocketActive()

// 临时断开连接（保留实例）
export function disconnectChatWebSocket()
```

#### 连接管理策略
- **启动时机：** 用户首次进入聊天功能时
- **保持策略：** 页面切换时连接保持活跃
- **关闭时机：** 仅在用户登出时关闭

### 2. 消息上下文严格判断

#### 统一消息添加方法
```javascript
addMessageToCurrentChat(message, source = 'unknown') {
  // 严格的上下文判断逻辑
  const belongsToCurrentConversation = currentPeerId && (
    // 情况1：我发给当前选中用户的消息
    (loginUserId && messageSenderId === loginUserId && messageReceiverId === currentPeerId)
    ||
    // 情况2：当前选中用户发给我的消息  
    (loginUserId && messageSenderId === currentPeerId && messageReceiverId === loginUserId)
  );
  
  if (belongsToCurrentConversation) {
    // 添加到当前聊天记录
  } else {
    // 过滤掉不属于当前会话的消息
  }
}
```

#### 判断条件
1. **发送者检查：** 消息发送者必须是当前用户或当前聊天对象
2. **接收者检查：** 消息接收者必须是当前用户或当前聊天对象
3. **会话匹配：** 消息必须属于当前打开的聊天会话

## 修改文件清单

### 1. src/utils/websocket.js
- ✅ 新增全局连接管理函数
- ✅ 添加连接状态跟踪
- ✅ 优化连接关闭逻辑

### 2. src/store/modules/chat.js
- ✅ 新增统一消息添加方法 `addMessageToCurrentChat`
- ✅ 修改 `addOneChatMsgDataByWS` 使用新的判断逻辑
- ✅ 优化 `handleNewChatMessage` 使用统一方法

### 3. src/components/chat/ChatPanel.vue
- ✅ 使用 `startGlobalChatWebSocket` 启动连接
- ✅ 移除 `onUnmounted` 中的连接关闭逻辑

### 4. src/views/qh/chat/index.vue
- ✅ 使用全局连接管理
- ✅ 保持连接持久化

### 5. src/store/modules/user.js
- ✅ 在用户登出时关闭WebSocket连接

### 6. src/views/qh/websocket-test/index.vue
- ✅ 创建测试页面验证改进效果

## 测试验证

### 连接持久化测试
1. 访问 `/qh/websocket-test/index` 页面
2. 启动全局连接
3. 切换到其他页面
4. 返回测试页面检查连接状态

### 消息上下文测试
1. 在测试页面点击"发送错误上下文消息"
2. 观察日志确认消息被正确过滤
3. 前往聊天页面测试正常消息收发

## 优势

1. **消息推送可靠性：** 用户在任何页面都能接收到消息
2. **上下文准确性：** 消息只会显示在正确的聊天窗口中
3. **资源优化：** 避免频繁的连接建立和断开
4. **用户体验：** 提升实时通信的稳定性

## 注意事项

1. **内存管理：** 长期保持连接需要注意内存泄漏
2. **错误处理：** 需要完善的重连和错误恢复机制
3. **性能监控：** 建议添加连接状态监控和日志
